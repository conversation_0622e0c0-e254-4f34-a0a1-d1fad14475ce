<?php

namespace App\Http\Controllers\es_syarat\spm;
use App\Http\Controllers\Controller;
use App\TawarKod;
use App\SyaratKhas;
use Illuminate\Http\Request;
use Auth;
use DB;
use PDF;

class es_SyaratKhasCTRL extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function index(Request $request)
    {
        $codeset_ipta = DB::connection('upucodeset')->table('refemas_institusi')->whereNotIn('kodipta',['1','2'])->orderBy('sorting', 'ASC')->get();
        $codeset_peringkat = DB::connection('upucodeset')->table('refemas_peringkat')->get();
        $codeset_bidang_nec = DB::connection('upucodeset')->table('refdip_bidangnec')->get();
        $codeset_oku = DB::connection('upucodeset')->table('refperibadi_jencacat')->get();

        // REQUEST URL
        $url=$request->url(); // SHORT PATH
        $fullUrl=$request->fullUrl(); // FULL PATH

        if($url==$fullUrl)
        {
            // CLEAR SESSION WHEN SAME URL PATH
            session()->forget(['program', 'ipta', 'kategori']);
        }

        if(session()->get('login_jenprog')=='spm')
        {
            $sessionSesi = session()->get('sesi_semasa');

            if(session()->get('kategori') =='spmA') { $kat= ['A']; }
            elseif(session()->get('kategori') =='spmB') { $kat= ['B']; }
            else { $kat= ['A','B']; }

            $program = DB::connection('emas')->table('program')
                ->whereIn('KATEGORI', $kat)
                ->where('STATUS_TAWAR','Y')
                ->where('SESI',$sessionSesi)
                ->when(in_array(Auth::user()->ipta, ['11','22','FA','OI','OP','IA']) && session()->get('ipta') != '', function ($q) {
                    return $q->where(DB::raw('substr(KODPROGRAM_PAPAR, 1, 2)'), 'like', '%'.session()->get('ipta').'%')
                            ->where('KODPROGRAM_PAPAR', 'like', '%'.session()->get('program').'%');
                })
                ->when(in_array(Auth::user()->ipta, ['11','22']) && session()->get('ipta') == '', function ($q) {
                    return $q->where(DB::raw('substr(KODPROGRAM_PAPAR, 1, 2)'), 'UA')
                            ->where('KODPROGRAM_PAPAR', 'like', '%'.session()->get('program').'%');
                })
                ->when(in_array(Auth::user()->ipta, ['FA']) && session()->get('ipta') == '', function ($q) {
                    return $q->where(DB::raw('substr(KODPROGRAM_PAPAR, 1, 2)'), 'FB')
                            ->where('KODPROGRAM_PAPAR', 'like', '%'.session()->get('program').'%');
                })
                ->when(in_array(Auth::user()->ipta, ['OI']) && session()->get('ipta') == '', function ($q) {
                    return $q->where(DB::raw('substr(KODPROGRAM_PAPAR, 1, 2)'), 'OI')
                            ->where('KODPROGRAM_PAPAR', 'like', '%'.session()->get('program').'%');
                })
                ->when(in_array(Auth::user()->ipta, ['OP']) && session()->get('ipta') == '', function ($q) {
                    return $q->where(DB::raw('substr(KODPROGRAM_PAPAR, 1, 2)'), 'OP')
                            ->where('KODPROGRAM_PAPAR', 'like', '%'.session()->get('program').'%');
                })
                ->when(in_array(Auth::user()->ipta, ['IA']) && session()->get('ipta') == '', function ($q) {
                    return $q->where(DB::raw('substr(KODPROGRAM_PAPAR, 1, 2)'), 'IA')
                            ->where('KODPROGRAM_PAPAR', 'like', '%'.session()->get('program').'%');
                })
                ->when(!in_array(Auth::user()->ipta, ['11','22','FA','OI','OP','IA']), function ($q) {
                    return $q->where(DB::raw('substr(KODPROGRAM_PAPAR, 1, 2)'), Auth::user()->ipta)
                            ->where('KODPROGRAM_PAPAR', 'like', '%'.session()->get('program').'%');
                })
                ->orderby('KODPROGRAM_PAPAR','ASC')
                ->orderby('KATEGORI','ASC')
                ->orderby('ALIRAN','ASC')
                ->paginate(10);


            $kod_program = DB::connection('emas')->table('program')
                ->where('LEPASAN',session()->get('login_jenprog'))
                ->where('SESI',$sessionSesi)
                ->where('STATUS_TAWAR','Y')
                ->when(in_array(Auth::user()->ipta, ['11','22','FA','OI','OP','IA']) && session()->get('ipta') != '', function ($q) {
                    return $q->where(DB::raw('substr(KODPROGRAM_PAPAR, 1, 2)'), 'like', '%'.session()->get('ipta').'%');
                })
                ->when(in_array(Auth::user()->ipta, ['11','22']) && session()->get('ipta') == '', function ($q) {
                    return $q->where(DB::raw('substr(KODPROGRAM_PAPAR, 1, 2)'), 'UA');
                })
                ->when(in_array(Auth::user()->ipta, ['FA']) && session()->get('ipta') == '', function ($q) {
                    return $q->where(DB::raw('substr(KODPROGRAM_PAPAR, 1, 2)'), 'FB');
                })
                ->when(in_array(Auth::user()->ipta, ['OI']) && session()->get('ipta') == '', function ($q) {
                    return $q->where(DB::raw('substr(KODPROGRAM_PAPAR, 1, 2)'), 'OI');
                })
                ->when(in_array(Auth::user()->ipta, ['OP']) && session()->get('ipta') == '', function ($q) {
                    return $q->where(DB::raw('substr(KODPROGRAM_PAPAR, 1, 2)'), 'OP');
                })
                ->when(in_array(Auth::user()->ipta, ['IA']) && session()->get('ipta') == '', function ($q) {
                    return $q->where(DB::raw('substr(KODPROGRAM_PAPAR, 1, 2)'), 'IA');
                })
                ->when(!in_array(Auth::user()->ipta, ['11','22','FA','OI','OP','IA']), function ($q) {
                    return $q->where(DB::raw('substr(KODPROGRAM_PAPAR, 1, 2)'), Auth::user()->ipta);
                })
                ->groupby('KODPROGRAM_PAPAR')
                ->get();

        }

        // TAMBAHAN UNTUK SYARAT KHAS BARU
        $skgred = DB::connection('upucodeset')->table('refspm_gred')->whereNotIn('kodspmgred',['R','T','X','Z'])->get();
        $sksubjek = DB::connection('upucodeset')->table('refspm_subjek')->whereNotIn('kodsubjekspm',['0000'])->get();

        include(app_path() . '/Http/Controllers/es_syarat/spm/db_view/subjek_kesetaraan.php');
        $sksubjek_1 = collect($subjek_kesetaraan);

        $syarat_kesetaraan = DB::connection('emas')->table('syarat_kesetaraan')->get();
        $syarat_sub_kumpulan = DB::connection('emas')->table('syarat_sub_kumpulan_subjek')->get();
        $syarat_x_sub_kumpulan = DB::connection('emas')->table('syarat_xsub_kumpulan_subjek')->get();

        if(Auth::user()->roles=='1' || Auth::user()->roles=='2' || Auth::user()->roles=='3' || Auth::user()->roles=='4' || Auth::user()->roles=='5' || Auth::user()->roles=='6' || Auth::user()->roles=='7' || Auth::user()->roles=='8' || Auth::user()->roles=='9')
        { return view('es_syaratkhas.spm.index',compact('program','kod_program','codeset_oku','sksubjek','sksubjek_1','syarat_kesetaraan','syarat_sub_kumpulan','syarat_x_sub_kumpulan')); }
        else { return view('lock.index'); }
    }

    public function showSpecificProgramPopup(Request $request, $programCode, $kategori = null)
    {
        $codeset_ipta = DB::connection('upucodeset')->table('refemas_institusi')->whereNotIn('kodipta',['1','2'])->orderBy('sorting', 'ASC')->get();
        $codeset_peringkat = DB::connection('upucodeset')->table('refemas_peringkat')->get();
        $codeset_bidang_nec = DB::connection('upucodeset')->table('refdip_bidangnec')->get();
        $codeset_oku = DB::connection('upucodeset')->table('refperibadi_jencacat')->get();

        $sessionSesi = session()->get('sesi_semasa');

        // Build query for specific program (SPM uses 'program' table, not 'program_stpm')
        $query = DB::connection('emas')->table('program')
            ->where('STATUS_TAWAR','Y')
            ->where('SESI',$sessionSesi)
            ->where('KODPROGRAM_PAPAR', $programCode);

        // Add category filter if provided (SPM only has A, B categories)
        if ($kategori) {
            $query->where('KATEGORI', $kategori);
        }

        $program = $query->paginate(10);

        // TAMBAHAN UNTUK SYARAT KHAS BARU
        $skgred = DB::connection('upucodeset')->table('refspm_gred')->whereNotIn('kodspmgred',['R','T','X','Z'])->get();
        $sksubjek = DB::connection('upucodeset')->table('refspm_subjek')->whereNotIn('kodsubjekspm',['0000'])->get();

        // Include subjek kesetaraan for SPM
        include(app_path() . '/Http/Controllers/es_syarat/spm/db_view/subjek_kesetaraan.php');
        $sksubjek_1 = collect($subjek_kesetaraan);

        $syarat_kesetaraan = DB::connection('emas')->table('syarat_kesetaraan')->get();
        $syarat_sub_kumpulan = DB::connection('emas')->table('syarat_sub_kumpulan_subjek')->get();
        $syarat_x_sub_kumpulan = DB::connection('emas')->table('syarat_xsub_kumpulan_subjek')->get();

        if(in_array(Auth::user()->roles, ['1','2','3','4','5','6','7','8','9']))
        {
            return view('es_syaratkhas.spm.popup', compact('program','codeset_oku','sksubjek','sksubjek_1',
                'syarat_kesetaraan','syarat_sub_kumpulan','syarat_x_sub_kumpulan','programCode','kategori'));
        }
        else { return view('lock.index'); }
    }

    public function khasfilter(Request $request)
    {
        if ($request->has('khas_cari'))
        {
            $program = $request->input('PROGRAM');
            $ipta = $request->input('IPTA');
            $kategori = $request->input('KATEGORI');
            $pendidikan = $request->input('DIDIK1');
            $ketidakupayaan = $request->input('CACAT1');

            // INJECT REQUEST TO SESSION AND WILL BE USE TO MANIPULATE IN DATA TABLE
            $request->session()->put(['program' => $program, 'ipta' => $ipta, 'kategori' => $kategori, 'pendidikan' => $pendidikan, 'ketidakupayaan' => $ketidakupayaan]);
        }

        if ($request->has('khas_reset'))
        {
            // CLEAR SESSION WHEN CLICK RESET BUTTON
            session()->forget(['program', 'ipta', 'kategori', 'pendidikan', 'ketidakupayaan']);
        }

        return redirect('esyaratkhas?page=1');
    }

    public function edit(Request $request, $sk_kodprogram)
    {
        // PERLU TUKAR MENGIKUT KESESUAIAN UNTUK KEMASKINI
        // FLAG UNTUK FASA KEMASKINI
        // 1 = BUKAN FASA TAFSIRAN 2 = FASA SEMASA/SELEPAS TAFSIRAN
        $fasa_kemaskini = '2';

        $codeset_ipta = DB::connection('upucodeset')->table('refemas_institusi')->whereNotIn('kodipta',['1','2'])->orderBy('sorting', 'ASC')->get();
        $codeset_peringkat = DB::connection('upucodeset')->table('refemas_peringkat')->get();
        $codeset_bidang_nec = DB::connection('upucodeset')->table('refalldip_bidangnec')->get();
        $codeset_syarat_kesetaraan = DB::connection('emas')->table('syarat_kesetaraan')->get();

        // TAMBAHAN UNTUK SYARAT KHAS BARU
        $skgred = DB::connection('upucodeset')->table('refspm_upuplus_gred')->orderby('sorting','ASC')->get();

        include(app_path() . '/Http/Controllers/es_syarat/spm/db_view/subjek_kesetaraan.php');
        $sksubjek = collect($subjek_kesetaraan)
        ->filter(function($item) use ($sk_kodprogram) {
            return in_array(substr($item->KOD, 0, 1), range(1, 9))
                   || substr($item->pemilik, 0, 2) === substr($sk_kodprogram, 0, 2);
        })
        ->sortBy('KOD')
        ->values()
        ->toArray();

        $edit_syaratkhas = DB::connection('emas')->table('program')->where('LEPASAN',session()->get('login_jenprog'))
        ->where('SESI',session()->get('sesi_semasa'))
        ->where('KODPROGRAM', $sk_kodprogram)
        ->get();

         return view('es_syaratkhas.spm.update', compact('edit_syaratkhas','codeset_ipta','codeset_peringkat','codeset_bidang_nec','skgred','sksubjek','codeset_syarat_kesetaraan','fasa_kemaskini'));
    }

    public function update(Request $request)
    {
        $st_fasa = $request->input('fasa');
        $st_id = $request->input('id');
        $st_program = $request->input('program');
        $st_kategori = $request->input('kategori');
        $st_subjek = $request->input('STSUBJEK');
        $st_gred = $request->input('STGRED');
        $st_susun = $request->input('susunid');
        $st_catat = $request->input('catatan');

        // SYARAT KUMPULAN 1
        $sk_susun_1 = $request->input('susun_kump1');
        $sk_subjek_1 = $request->input('SKSUBJEK1');
        $sk_jum1 = $request->input('SK_JUM1');
        $sk_jum2 = $request->input('SK_JUM2');
        $sk_gred1_kump1 = $request->input('SKGRED1_KUMP1');
        $sk_gred2_kump1 = $request->input('SKGRED2_KUMP1');
        $susun_subjekG1 = $request->input('susun_subjekG1');

        // SYARAT KUMPULAN 2
        $sk_susun_2 = $request->input('susun_kump2');
        $sk_subjek_2 = $request->input('SKSUBJEK2');
        $sk_jum3 = $request->input('SK_JUM3');
        $sk_jum4 = $request->input('SK_JUM4');
        $sk_gred1_kump2 = $request->input('SKGRED1_KUMP2');
        $sk_gred2_kump2 = $request->input('SKGRED2_KUMP2');
        $susun_subjekG2 = $request->input('susun_subjekG2');

        // SYARAT KUMPULAN 3
        $sk_susun_3 = $request->input('susun_kump3');
        $sk_subjek_3 = $request->input('SKSUBJEK3');
        $sk_jum5 = $request->input('SK_JUM5');
        $sk_gred1_kump3 = $request->input('SKGRED1_KUMP3');
        $susun_subjekG3 = $request->input('susun_subjekG3');


        // SYARAT FLEKSIBEL 1
        $sf_susun_1 = $request->input('susun_flexi1');
        $sf_gred1_flexi1 = $request->input('SFGRED_FLEXI1');
        $sf_jum1 = $request->input('SF_JUMF1');
        $sf_syarat_flexi1 = $request->input('SF_XSYARAT1');
        $sf_kepil_1 = $request->input('SF_DIKEPILKAN1');

        // SYARAT FLEKSIBEL 2
        $sf_susun_2 = $request->input('susun_flexi2');
        $sf_gred1_flexi2 = $request->input('SFGRED_FLEXI2');
        $sf_jum2  = $request->input('SF_JUMF2');
        $sf_syarat_flexi2 = $request->input('SF_XSYARAT2');
        $sf_kepil_2 = $request->input('SF_DIKEPILKAN2');

        // SYARAT FLEKSIBEL 3
        $sf_susun_3 = $request->input('susun_flexi3');
        $sf_gred1_flexi3 = $request->input('SFGRED_FLEXI3');
        $sf_jum3  = $request->input('SF_JUMF3');
        $sf_subjek_1 = $request->input('SFSUBJEKF3');
        $susun_subjekF3 = $request->input('susun_subjekF3');

        $delete_st = DB::connection('emas')
        ->table('syarat_khas')
		->where('SESI', session()->get('sesi_semasa'))
        ->where('PROGRAMKOD', $st_id)
        ->delete();

        $delete_skumpulan_ = DB::connection('emas')
        ->table('syarat_sub_kumpulan_subjek')
		->where('SESI', session()->get('sesi_semasa'))
        ->where(DB::raw('SUBSTR(KUMPULAN,3,LENGTH(KUMPULAN))'), $st_id)
        ->delete();

        $delete_skumpulan_x1 = DB::connection('emas')
        ->table('syarat_xsub_kumpulan_subjek')
		->where('SESI', session()->get('sesi_semasa'))
        ->where(DB::raw('SUBSTR(KUMPULAN,3,LENGTH(KUMPULAN))'), $st_id)
        ->delete();

        $delete_skumpulan_x2 = DB::connection('emas')
        ->table('syarat_xsub_kumpulan_subjek')
		->where('SESI', session()->get('sesi_semasa'))
        ->where(DB::raw('SUBSTR(KUMPULAN,2,LENGTH(KUMPULAN))'), $st_id)
        ->delete();

        $sessionSesi = session()->get('sesi_semasa');
        // SYARAT TUNGGAL ##############################################################################################################################################################################
        $count_st = count($st_subjek);
        if ($count_st != 0)
        {
            if (($st_gred > 0) && ($st_subjek[0] != ''))
            {
                for($i = 0; $i<$count_st; $i++)
                {
                    $insert_st = DB::connection('emas')
                    ->table('syarat_khas')
                    ->insert(
                        [
                            'PROGRAMKOD' => $st_id,
                            'KODSUBJEK' => $st_subjek[$i],
                            'MINGRED' => $st_gred[$i],
                            'KUMPULAN' => 'N',
                            'SUB_KUMPULAN' => 'N',
                            'JUMLAH_MIN_SUBJEK' => '1',
                            'ORDERID' => $st_susun[$i],
                            'SESI' => session()->get('sesi_semasa'),
                            'fasa' => $st_fasa,
                            'UPDATED_AT' => now()
                        ]);
                }

                // CHECK AND GET VALUE TO CREATE LOG
                include(app_path() . '/Http/Controllers/es_syarat/spm/db_view/syaratkhas_nn.php');
                $count = count($get_syarat_nn);

                if ($count != 0)
                {
                    for($i = 0; $i<$count; $i++)
                    {
                        $insert_log_syarat = DB::connection('emas')
                        ->table('upu_log_syarat')
                        ->insert(
                            [
                                'PROGRAMKOD' => $get_syarat_nn[$i]->PROGRAMKOD,
                                'KODSUBJEK_1' => $get_syarat_nn[$i]->KODSUBJEK_1,
                                'KODSUBJEK_2' => $get_syarat_nn[$i]->KODSUBJEK_2,
                                'MINGRED' => $get_syarat_nn[$i]->MINGRED,
                                'KUMPULAN' => $get_syarat_nn[$i]->KUMPULAN,
                                'SUB_KUMPULAN' => $get_syarat_nn[$i]->SUB_KUMPULAN,
                                'KET_JUMLAH_MIN_SUBJEK' => $get_syarat_nn[$i]->KET_JUMLAH_MIN_SUBJEK,
                                'JUMLAH_MIN_SUBJEK' => $get_syarat_nn[$i]->JUMLAH_MIN_SUBJEK,
                                'SESI' => $get_syarat_nn[$i]->SESI,
                                'ORDERID' => $get_syarat_nn[$i]->ORDERID,
                                'ORDERID2' => $get_syarat_nn[$i]->ORDERID2,
                                'JENIS_SYARAT' => 'ST',
                                'FASA' => $st_fasa,
                                'UPDATED_USER' => Auth::user()->user_id,
                                'UPDATED_AT' => now()
                            ]);
                    }
                }
            }
        }

        // SYARAT KUMPULAN 1 ##############################################################################################################################################################################

            if (($sk_subjek_1 > 0) && ($sk_subjek_1[0] != '') && ($sk_jum1 != '') && ($sk_gred1_kump1 != ''))
            {
                $insert_st = DB::connection('emas')
                ->table('syarat_khas')
                ->insert(
                    [
                        'PROGRAMKOD' => $st_id,
                        'KODSUBJEK' => 'G1'.$st_id,
                        'MINGRED' => $sk_gred1_kump1,
                        'KUMPULAN' => 'Y',
                        'SUB_KUMPULAN' => 'N',
                        'JUMLAH_MIN_SUBJEK' => $sk_jum1,
                        'ORDERID' => $sk_susun_1,
                        'SESI' => session()->get('sesi_semasa'),
                        'fasa' => $st_fasa,
                        'UPDATED_AT' => now()
                    ]);

                if ($sf_syarat_flexi1 != 'X1')
                {


                    for ($i = 0; $i < count($sk_subjek_1); $i++)
                    {
                        $insert_st = DB::connection('emas')
                        ->table('syarat_sub_kumpulan_subjek')
                        ->insert(
                            [
                                'KUMPULAN' => 'G1'.$st_id,
                                'KODSUBJEK' => $sk_subjek_1[$i],
                                'SESI' => session()->get('sesi_semasa'),
                                'orderid' => $susun_subjekG1[$i],
                            ]);
                    }
                }

                // CHECK AND GET VALUE TO CREATE LOG
                include(app_path() . '/Http/Controllers/es_syarat/spm/db_view/syaratkhas_g1.php');
                $countg1 = count($get_syarat_g1);

                if ($countg1 != 0)
                {
                    for($i = 0; $i<$countg1; $i++)
                    {
                        $insert_log_syarat = DB::connection('emas')
                        ->table('upu_log_syarat')
                        ->insert(
                            [
                                'PROGRAMKOD' => $get_syarat_g1[$i]->PROGRAMKOD,
                                'GKUMPULAN' => $get_syarat_g1[$i]->GKUMPULAN,
                                'KODSUBJEK_1' => $get_syarat_g1[$i]->KODSUBJEK_1,
                                'KODSUBJEK_2' => $get_syarat_g1[$i]->KODSUBJEK_2,
                                'MINGRED' => $get_syarat_g1[$i]->MINGRED,
                                'KUMPULAN' => $get_syarat_g1[$i]->KUMPULAN,
                                'SUB_KUMPULAN' => $get_syarat_g1[$i]->SUB_KUMPULAN,
                                'KET_JUMLAH_MIN_SUBJEK' => $get_syarat_g1[$i]->KET_JUMLAH_MIN_SUBJEK,
                                'JUMLAH_MIN_SUBJEK' => $get_syarat_g1[$i]->JUMLAH_MIN_SUBJEK,
                                'SESI' => $get_syarat_g1[$i]->SESI,
                                'ORDERID' => $get_syarat_g1[$i]->ORDERID,
                                'ORDERID2' => $get_syarat_g1[$i]->ORDERID2,
                                'JENIS_SYARAT' => 'SK1',
                                'FASA' => $st_fasa,
                                'UPDATED_USER' => Auth::user()->user_id,
                                'UPDATED_AT' => now()
                            ]);
                    }
                }
            }

            if (($sk_subjek_1 > 0) && ($sk_subjek_1[0] != '') && ($sk_jum2 != '') && ($sk_gred2_kump1 != ''))
            {

                $insert_st = DB::connection('emas')
                ->table('syarat_khas')
                ->insert(
                    [
                        'PROGRAMKOD' => $st_id,
                        'KODSUBJEK' => 'GA'.$st_id,
                        'MINGRED' => $sk_gred2_kump1,
                        'KUMPULAN' => 'Y',
                        'SUB_KUMPULAN' => 'N',
                        'JUMLAH_MIN_SUBJEK' => $sk_jum2,
                        'ORDERID' => $sk_susun_1,
                        'SESI' => session()->get('sesi_semasa'),
                        'fasa' => $st_fasa,
                        'UPDATED_AT' => now()
                    ]);


                if ($sf_syarat_flexi1 != 'X1')
                {

                    // $count_st = count($sk_subjek_1);
                    for ($i = 0; $i <  count($sk_subjek_1); $i++)
                    {
                        $insert_st = DB::connection('emas')
                        ->table('syarat_sub_kumpulan_subjek')
                        ->insert(
                            [
                                'KUMPULAN' => 'GA'.$st_id,
                                'KODSUBJEK' => $sk_subjek_1[$i],
                                'SESI' => session()->get('sesi_semasa'),
                                'orderid' => $susun_subjekG1[$i],
                            ]);
                    }
                }


                // CHECK AND GET VALUE TO CREATE LOG
                include(app_path() . '/Http/Controllers/es_syarat/spm/db_view/syaratkhas_ga.php');
                $countga = count($get_syarat_ga);

                if ($countga != 0)
                {
                    for($i = 0; $i<$countga; $i++)
                    {
                        $insert_log_syarat = DB::connection('emas')
                        ->table('upu_log_syarat')
                        ->insert(
                            [
                                'PROGRAMKOD' => $get_syarat_ga[$i]->PROGRAMKOD,
                                'GKUMPULAN' => $get_syarat_ga[$i]->GKUMPULAN,
                                'KODSUBJEK_1' => $get_syarat_ga[$i]->KODSUBJEK_1,
                                'KODSUBJEK_2' => $get_syarat_ga[$i]->KODSUBJEK_2,
                                'MINGRED' => $get_syarat_ga[$i]->MINGRED,
                                'KUMPULAN' => $get_syarat_ga[$i]->KUMPULAN,
                                'SUB_KUMPULAN' => $get_syarat_ga[$i]->SUB_KUMPULAN,
                                'KET_JUMLAH_MIN_SUBJEK' => $get_syarat_ga[$i]->KET_JUMLAH_MIN_SUBJEK,
                                'JUMLAH_MIN_SUBJEK' => $get_syarat_ga[$i]->JUMLAH_MIN_SUBJEK,
                                'SESI' => $get_syarat_ga[$i]->SESI,
                                'ORDERID' => $get_syarat_ga[$i]->ORDERID,
                                'ORDERID2' => $get_syarat_ga[$i]->ORDERID2,
                                'JENIS_SYARAT' => 'SKA',
                                'FASA' => $st_fasa,
                                'UPDATED_USER' => Auth::user()->user_id,
                                'UPDATED_AT' => now()
                            ]);
                    }
                }

            }

        // SYARAT KUMPULAN 2 ##########################################################

        if (($sk_subjek_2 > 0) && ($sk_subjek_2[0] != '') && ($sk_jum3 != '') && ($sk_gred1_kump2 != ''))
        {
            $insert_st = DB::connection('emas')
            ->table('syarat_khas')
            ->insert(
                [
                    'PROGRAMKOD' => $st_id,
                    'KODSUBJEK' => 'G2'.$st_id,
                    'MINGRED' => $sk_gred1_kump2,
                    'KUMPULAN' => 'Y',
                    'SUB_KUMPULAN' => 'N',
                    'JUMLAH_MIN_SUBJEK' => $sk_jum3,
                    'ORDERID' => $sk_susun_2,
                    'SESI' => session()->get('sesi_semasa'),
                    'fasa' => $st_fasa,
                    'UPDATED_AT' => now()
                ]);

                for ($i = 0; $i < count($sk_subjek_2); $i++)
                {
                    $insert_st = DB::connection('emas')
                    ->table('syarat_sub_kumpulan_subjek')
                    ->insert(
                        [
                            'KUMPULAN' => 'G2'.$st_id,
                            'KODSUBJEK' => $sk_subjek_2[$i],
                            'SESI' => session()->get('sesi_semasa'),
                            'orderid' => $susun_subjekG2[$i],

                        ]);
                }

                // CHECK AND GET VALUE TO CREATE LOG
                include(app_path() . '/Http/Controllers/es_syarat/spm/db_view/syaratkhas_g2.php');
                $countg2 = count($get_syarat_g2);

                if ($countg2 != 0)
                {
                    for($i = 0; $i<$countg2; $i++)
                    {
                        $insert_log_syarat = DB::connection('emas')
                        ->table('upu_log_syarat')
                        ->insert(
                            [
                                'PROGRAMKOD' => $get_syarat_g2[$i]->PROGRAMKOD,
                                'GKUMPULAN' => $get_syarat_g2[$i]->GKUMPULAN,
                                'KODSUBJEK_1' => $get_syarat_g2[$i]->KODSUBJEK_1,
                                'KODSUBJEK_2' => $get_syarat_g2[$i]->KODSUBJEK_2,
                                'MINGRED' => $get_syarat_g2[$i]->MINGRED,
                                'KUMPULAN' => $get_syarat_g2[$i]->KUMPULAN,
                                'SUB_KUMPULAN' => $get_syarat_g2[$i]->SUB_KUMPULAN,
                                'KET_JUMLAH_MIN_SUBJEK' => $get_syarat_g2[$i]->KET_JUMLAH_MIN_SUBJEK,
                                'JUMLAH_MIN_SUBJEK' => $get_syarat_g2[$i]->JUMLAH_MIN_SUBJEK,
                                'SESI' => $get_syarat_g2[$i]->SESI,
                                'ORDERID' => $get_syarat_g2[$i]->ORDERID,
                                'ORDERID2' => $get_syarat_g2[$i]->ORDERID2,
                                'JENIS_SYARAT' => 'SK2',
                                'FASA' => $st_fasa,
                                'UPDATED_USER' => Auth::user()->user_id,
                                'UPDATED_AT' => now()
                            ]);
                    }
                }
        }

        if (($sk_subjek_2 > 0) && ($sk_subjek_2[0] != '') &&  ($sk_jum4 != '') && ($sk_gred2_kump2 != ''))
        {
            $insert_st = DB::connection('emas')
            ->table('syarat_khas')
            ->insert(
                [
                    'PROGRAMKOD' => $st_id,
                    'KODSUBJEK' => 'GB'.$st_id,
                    'MINGRED' => $sk_gred2_kump2,
                    'KUMPULAN' => 'Y',
                    'SUB_KUMPULAN' => 'N',
                    'JUMLAH_MIN_SUBJEK' => $sk_jum4,
                    'ORDERID' => $sk_susun_2,
                    'SESI' => session()->get('sesi_semasa'),
                    'fasa' => $st_fasa,
                    'UPDATED_AT' => now()
                ]);

                if ($sf_syarat_flexi1 != 'X2')
                {
                    for ($i = 0; $i < count($sk_subjek_2); $i++)
                    {
                        $insert_st = DB::connection('emas')
                        ->table('syarat_sub_kumpulan_subjek')
                        ->insert(
                            [
                                'KUMPULAN' => 'GB'.$st_id,
                                'KODSUBJEK' => $sk_subjek_2[$i],
                                'SESI' => session()->get('sesi_semasa'),
                                'orderid' => $susun_subjekG2[$i],
                            ]);
                    }
                }

                // CHECK AND GET VALUE TO CREATE LOG
                include(app_path() . '/Http/Controllers/es_syarat/spm/db_view/syaratkhas_gb.php');
                $countgb = count($get_syarat_gb);

                if ($countgb != 0)
                {
                    for($i = 0; $i<$countgb; $i++)
                    {
                        $insert_log_syarat = DB::connection('emas')
                        ->table('upu_log_syarat')
                        ->insert(
                            [
                                'PROGRAMKOD' => $get_syarat_gb[$i]->PROGRAMKOD,
                                'GKUMPULAN' => $get_syarat_gb[$i]->GKUMPULAN,
                                'KODSUBJEK_1' => $get_syarat_gb[$i]->KODSUBJEK_1,
                                'KODSUBJEK_2' => $get_syarat_gb[$i]->KODSUBJEK_2,
                                'MINGRED' => $get_syarat_gb[$i]->MINGRED,
                                'KUMPULAN' => $get_syarat_gb[$i]->KUMPULAN,
                                'SUB_KUMPULAN' => $get_syarat_gb[$i]->SUB_KUMPULAN,
                                'KET_JUMLAH_MIN_SUBJEK' => $get_syarat_gb[$i]->KET_JUMLAH_MIN_SUBJEK,
                                'JUMLAH_MIN_SUBJEK' => $get_syarat_gb[$i]->JUMLAH_MIN_SUBJEK,
                                'SESI' => $get_syarat_gb[$i]->SESI,
                                'ORDERID' => $get_syarat_gb[$i]->ORDERID,
                                'ORDERID2' => $get_syarat_gb[$i]->ORDERID2,
                                'JENIS_SYARAT' => 'SKB',
                                'FASA' => $st_fasa,
                                'UPDATED_USER' => Auth::user()->user_id,
                                'UPDATED_AT' => now()
                            ]);
                    }
                }
        }

        // SYARAT KUMPULAN 3 ##########################################################

        if (($sk_subjek_3 > 0) && ($sk_subjek_3[0] != '') && ($sk_jum5 != '') && ($sk_gred1_kump3 != ''))
        {
            $insert_st = DB::connection('emas')
            ->table('syarat_khas')
            ->insert(
                [
                    'PROGRAMKOD' => $st_id,
                    'KODSUBJEK' => 'G3'.$st_id,
                    'MINGRED' => $sk_gred1_kump3,
                    'KUMPULAN' => 'Y',
                    'SUB_KUMPULAN' => 'N',
                    'JUMLAH_MIN_SUBJEK' => $sk_jum5,
                    'ORDERID' => $sk_susun_3,
                    'SESI' => session()->get('sesi_semasa'),
                    'fasa' => $st_fasa,
                    'UPDATED_AT' => now()
                ]);

            for ($i = 0; $i < count($sk_subjek_3); $i++)
            {
                $insert_st = DB::connection('emas')
                ->table('syarat_sub_kumpulan_subjek')
                ->insert(
                    [
                        'KUMPULAN' => 'G3'.$st_id,
                        'KODSUBJEK' => $sk_subjek_3[$i],
                        'SESI' => session()->get('sesi_semasa'),
                        'orderid' => $susun_subjekG3[$i],
                    ]);
            }

                // CHECK AND GET VALUE TO CREATE LOG
                include(app_path() . '/Http/Controllers/es_syarat/spm/db_view/syaratkhas_g3.php');
                $countg3 = count($get_syarat_g3);

                if ($countg3 != 0)
                {
                    for($i = 0; $i<$countg3; $i++)
                    {
                        $insert_log_syarat = DB::connection('emas')
                        ->table('upu_log_syarat')
                        ->insert(
                            [
                                'PROGRAMKOD' => $get_syarat_g3[$i]->PROGRAMKOD,
                                'GKUMPULAN' => $get_syarat_g3[$i]->GKUMPULAN,
                                'KODSUBJEK_1' => $get_syarat_g3[$i]->KODSUBJEK_1,
                                'KODSUBJEK_2' => $get_syarat_g3[$i]->KODSUBJEK_2,
                                'MINGRED' => $get_syarat_g3[$i]->MINGRED,
                                'KUMPULAN' => $get_syarat_g3[$i]->KUMPULAN,
                                'SUB_KUMPULAN' => $get_syarat_g3[$i]->SUB_KUMPULAN,
                                'KET_JUMLAH_MIN_SUBJEK' => $get_syarat_g3[$i]->KET_JUMLAH_MIN_SUBJEK,
                                'JUMLAH_MIN_SUBJEK' => $get_syarat_g3[$i]->JUMLAH_MIN_SUBJEK,
                                'SESI' => $get_syarat_g3[$i]->SESI,
                                'ORDERID' => $get_syarat_g3[$i]->ORDERID,
                                'ORDERID2' => $get_syarat_g3[$i]->ORDERID2,
                                'JENIS_SYARAT' => 'SK3',
                                'FASA' => $st_fasa,
                                'UPDATED_USER' => Auth::user()->user_id,
                                'UPDATED_AT' => now()
                            ]);
                    }
                }
        }

        // SYARAT FLEKSIBEL 1 ##############################################################################################################################################################################

        if (!empty($sf_gred1_flexi1) && !empty($sf_jum1))
        {

            if ($sf_syarat_flexi1 == 'F') // Dalam syarat kumpulan 1
            {
                $sub_Kumpulan ='F'; // Ambil semua walaupun dah dikira
            }
            else if ($sf_syarat_flexi1 == 'X') // Mana2 matapelajaran yang belum dikira
            {
                  $sub_Kumpulan ='X';
            }
            else if ($sf_syarat_flexi1 == 'Y') // Mana2 matapelajaran yang belum dikira
            {
                  $sub_Kumpulan ='Y';
            }
            else if ($sf_syarat_flexi1 == 'X1') // Dalam syarat kumpulan 1
            {
              $sub_Kumpulan ='X1';
            }
            else if ($sf_syarat_flexi1 == 'X2') { // Dalam mana2 matapelajaran
                $sub_Kumpulan ='X2';
            }
            else { $sub_Kumpulan ='';  }

            $insert_st = DB::connection('emas')
            ->table('syarat_khas')
            ->insert(
                [
                    'PROGRAMKOD' => $st_id,
                    'KODSUBJEK' => 'F'.$st_id,
                    'MINGRED' => $sf_gred1_flexi1,
                    'KUMPULAN' => 'F',
                    'SUB_KUMPULAN' => $sub_Kumpulan,
                    'JUMLAH_MIN_SUBJEK' => $sf_jum1,
                    'ORDERID' => $sf_susun_1,
                    'SESI' => session()->get('sesi_semasa'),
                    'fasa' => $st_fasa,
                    'UPDATED_AT' => now()
                ]);

            //Jika syarat fleksi tidak termasuk dalam Syarat Kumpulan 1
            if ($sf_syarat_flexi1 == 'X1')
            {
                //Jika Syarat kumpulan1 tidak null
                if (($sk_subjek_1 > 0) && ($sk_subjek_1[0] !== ''))
                {
                    for ($i = 0; $i < count($sk_subjek_1); $i++)
                    {
                        $insert_st = DB::connection('emas')
                        ->table('syarat_xsub_kumpulan_subjek')
                        ->insert(
                            [
                                'KUMPULAN' => 'F'.$st_id,
                                'KODSUBJEK' => $sk_subjek_1[$i],
                                'SESI' => session()->get('sesi_semasa')
                            ]);
                    }
                }
            }

            if (!empty($sf_kepil_1))
            {

                $sub_kumpulan1 = 'G'.$sf_kepil_1.$st_id;

                $insert_st = DB::connection('emas')
                ->table('syarat_khas')
                ->insert(
                    [
                        'PROGRAMKOD' => $st_id,
                        'KODSUBJEK' => 'F'.$st_id,
                        'MINGRED' => $sf_gred1_flexi1,
                        'KUMPULAN' => 'F',
                        'SUB_KUMPULAN' => $sub_kumpulan1,
                        'JUMLAH_MIN_SUBJEK' => $sf_jum1,
                        'ORDERID' => $sf_susun_1,
                        'SESI' => session()->get('sesi_semasa'),
                        'fasa' => $st_fasa,
                        'UPDATED_AT' => now()
                    ]);
            }

			// CHECK AND GET VALUE TO CREATE LOG
			include(app_path() . '/Http/Controllers/es_syarat/spm/db_view/syaratkhas_f1.php');
			$countf1 = count($get_syarat_f1);

			if ($countf1 != 0)
			{
				for($i = 0; $i<$countf1; $i++)
				{
					$insert_log_syarat = DB::connection('emas')
					->table('upu_log_syarat')
					->insert(
						[
							'PROGRAMKOD' => $get_syarat_f1[$i]->PROGRAMKOD,
							'GKUMPULAN' => $get_syarat_f1[$i]->GKUMPULAN,
							'KODSUBJEK_1' => $get_syarat_f1[$i]->KODSUBJEK_1,
							'KODSUBJEK_2' => $get_syarat_f1[$i]->KODSUBJEK_2,
							'MINGRED' => $get_syarat_f1[$i]->MINGRED,
							'KUMPULAN' => $get_syarat_f1[$i]->KUMPULAN,
							'SUB_KUMPULAN' => $get_syarat_f1[$i]->SUB_KUMPULAN,
							'KET_JUMLAH_MIN_SUBJEK' => $get_syarat_f1[$i]->KET_JUMLAH_MIN_SUBJEK,
							'JUMLAH_MIN_SUBJEK' => $get_syarat_f1[$i]->JUMLAH_MIN_SUBJEK,
							'SESI' => $get_syarat_f1[$i]->SESI,
							'ORDERID' => $get_syarat_f1[$i]->ORDERID,
							'ORDERID2' => $get_syarat_f1[$i]->ORDERID2,
							'JENIS_SYARAT' => 'SF1',
							'FASA' => $st_fasa,
							'UPDATED_USER' => Auth::user()->user_id,
							'UPDATED_AT' => now()
						]);
				}
			}
        }

        // SYARAT FLEKSIBEL 2 ##########################################################

        if (!empty($sf_gred1_flexi2) && !empty($sf_jum2))
        {
            if ($sf_syarat_flexi2 == 'F') // Dalam syarat kumpulan 1
            {
                $sub_Kumpulan2 ='F'; // Ambil semua walaupun dah dikira
            }
            else if ($sf_syarat_flexi2 == 'X') // Mana2 matapelajaran yang belum dikira
            {
                $sub_Kumpulan2 ='X';
            }
            else if ($sf_syarat_flexi2 == 'Y') // Mana2 matapelajaran selain diatas
            {
                $sub_Kumpulan2 ='Y';
            }
            else if ($sf_syarat_flexi2 == 'X1') // Dalam syarat kumpulan 1
            {
              $sub_Kumpulan2 ='X1';
            }
            else if ($sf_syarat_flexi2 == 'X2') { // Dalam mana2 matapelajaran
                $sub_Kumpulan2 ='X2';
            }

            $insert_st = DB::connection('emas')
            ->table('syarat_khas')
            ->insert(
                [
                    'PROGRAMKOD' => $st_id,
                    'KODSUBJEK' => 'F2'.$st_id,
                    'MINGRED' => $sf_gred1_flexi2,
                    'KUMPULAN' => 'F2',
                    'SUB_KUMPULAN' => $sub_Kumpulan2,
                    'JUMLAH_MIN_SUBJEK' => $sf_jum2,
                    'ORDERID' => $sf_susun_2,
                    'SESI' => session()->get('sesi_semasa'),
                    'fasa' => $st_fasa,
                    'UPDATED_AT' => now()
                ]);


                // CHECK AND GET VALUE TO CREATE LOG
                include(app_path() . '/Http/Controllers/es_syarat/spm/db_view/syaratkhas_f2.php');
                $countf2 = count($get_syarat_f2);

                if ($countf2 != 0)
                {
                    for($i = 0; $i<$countf2; $i++)
                    {
                        $insert_log_syarat = DB::connection('emas')
                        ->table('upu_log_syarat')
                        ->insert(
                            [
                                'PROGRAMKOD' => $get_syarat_f2[$i]->PROGRAMKOD,
                                'GKUMPULAN' => $get_syarat_f2[$i]->GKUMPULAN,
                                'KODSUBJEK_1' => $get_syarat_f2[$i]->KODSUBJEK_1,
                                'KODSUBJEK_2' => $get_syarat_f2[$i]->KODSUBJEK_2,
                                'MINGRED' => $get_syarat_f2[$i]->MINGRED,
                                'KUMPULAN' => $get_syarat_f2[$i]->KUMPULAN,
                                'SUB_KUMPULAN' => $get_syarat_f2[$i]->SUB_KUMPULAN,
                                'KET_JUMLAH_MIN_SUBJEK' => $get_syarat_f2[$i]->KET_JUMLAH_MIN_SUBJEK,
                                'JUMLAH_MIN_SUBJEK' => $get_syarat_f2[$i]->JUMLAH_MIN_SUBJEK,
                                'SESI' => $get_syarat_f2[$i]->SESI,
                                'ORDERID' => $get_syarat_f2[$i]->ORDERID,
                                'ORDERID2' => $get_syarat_f2[$i]->ORDERID2,
                                'JENIS_SYARAT' => 'SF2',
                                'FASA' => $st_fasa,
                                'UPDATED_USER' => Auth::user()->user_id,
                                'UPDATED_AT' => now()
                            ]);
                    }
                }

        }

        // SYARAT FLEKSIBEL 3 ##########################################################

        if (($sf_subjek_1 > 0) && ($sf_subjek_1[0] != '') && ($sf_jum3 != '') && ($sf_gred1_flexi3 != ''))
        {
            $insert_st = DB::connection('emas')
            ->table('syarat_khas')
            ->insert(
                [
                    'PROGRAMKOD' => $st_id,
                    'KODSUBJEK' => 'F3'.$st_id,
                    'MINGRED' => $sf_gred1_flexi3,
                    'KUMPULAN' => 'F3',
                    'SUB_KUMPULAN' => 'F3',
                    'JUMLAH_MIN_SUBJEK' => $sf_jum3,
                    'ORDERID' => $sf_susun_3,
                    'SESI' => session()->get('sesi_semasa'),
                    'fasa' => $st_fasa,
                    'UPDATED_AT' => now()
                ]);

            for ($i = 0; $i < count($sf_subjek_1); $i++)
            {
                $insert_st = DB::connection('emas')
                ->table('syarat_xsub_kumpulan_subjek')
                ->insert(
                    [
                        'KUMPULAN' => 'F3'.$st_id,
                        'KODSUBJEK' => $sf_subjek_1[$i],
                        'SESI' => session()->get('sesi_semasa'),
                        'orderid' => $susun_subjekF3[$i],
                    ]);
            }

                // CHECK AND GET VALUE TO CREATE LOG
                include(app_path() . '/Http/Controllers/es_syarat/spm/db_view/syaratkhas_f3.php');
                $countf3 = count($get_syarat_f3);

                if ($countf3 != 0)
                {
                    for($i = 0; $i<$countf3; $i++)
                    {
                        $insert_log_syarat = DB::connection('emas')
                        ->table('upu_log_syarat')
                        ->insert(
                            [
                                'PROGRAMKOD' => $get_syarat_f3[$i]->PROGRAMKOD,
                                'GKUMPULAN' => $get_syarat_f3[$i]->GKUMPULAN,
                                'KODSUBJEK_1' => $get_syarat_f3[$i]->KODSUBJEK_1,
                                'KODSUBJEK_2' => $get_syarat_f3[$i]->KODSUBJEK_2,
                                'MINGRED' => $get_syarat_f3[$i]->MINGRED,
                                'KUMPULAN' => $get_syarat_f3[$i]->KUMPULAN,
                                'SUB_KUMPULAN' => $get_syarat_f3[$i]->SUB_KUMPULAN,
                                'KET_JUMLAH_MIN_SUBJEK' => $get_syarat_f3[$i]->KET_JUMLAH_MIN_SUBJEK,
                                'JUMLAH_MIN_SUBJEK' => $get_syarat_f3[$i]->JUMLAH_MIN_SUBJEK,
                                'SESI' => $get_syarat_f3[$i]->SESI,
                                'ORDERID' => $get_syarat_f3[$i]->ORDERID,
                                'ORDERID2' => $get_syarat_f3[$i]->ORDERID2,
                                'JENIS_SYARAT' => 'SF3',
                                'FASA' => $st_fasa,
                                'UPDATED_USER' => Auth::user()->user_id,
                                'UPDATED_AT' => now()
                            ]);
                    }
                }
        }

        $sl_kahwin = $request->input('SL_KAHWIN');
        if($sl_kahwin=='B') { $kahwin='1'; } else { $kahwin='0'; }

        $sl_jantina = $request->input('SL_JANTINA');
        if($sl_jantina=='L' || $sl_jantina=='P') { $jantina='1'; } else { $jantina='0'; }

        $sl_opumur = $request->input('opumur');
        if($sl_opumur == 'T') { $opumur='0'; } else { $opumur='1'; }

        $sl_umur = $request->input('umur');
        $sl_bulan = $request->input('bulan');

        $total_bulan = (($sl_umur*12) + $sl_bulan);

        $sl_umur1 = $request->input('umur1');
        $sl_bulan1 = $request->input('bulan1');

        $total_bulan1 = (($sl_umur1*12) + $sl_bulan1);

        $sl_3m = $request->input('SL_3M');

        $valid_oku = \DB::connection('emas')->table('program')->select('OKU')->where('KODPROGRAM', $st_id)->where('LEPASAN','SPM')->where('SESI', session()->get('sesi_semasa'))->get();

        if($valid_oku[0]->OKU=='T' || $valid_oku[0]->OKU=='N') {$oku='0';} elseif($valid_oku[0]->OKU=='Y') { $oku='1'; } else { $oku='0'; }

        $count_syarat_lain = \DB::connection('emas')->table('syarat_lain')->where('Programkod', $st_id)->where('Kategori','SPM')->where('sesi', session()->get('sesi_semasa'))->get();
        $valid_syarat_lain = count($count_syarat_lain);

        if($valid_syarat_lain > 0)
        {
            $update_syarat_lain = DB::connection('emas')
            ->table('syarat_lain')
            ->where('Programkod', $st_id)
            ->where('Kategori','SPM')
            ->where('sesi', session()->get('sesi_semasa'))
            ->update(
                [
                    'Syarat_taraf_perkahwinan' => $kahwin ,
                    'Taraf_perkahwinan' => $sl_kahwin,
                    'Syarat_Jantina' => $jantina,
                    'Jantina' => $sl_jantina,
                    'Syarat_Umur' => $opumur,
                    'Operasi_Umur' => $sl_opumur,
                    'Umur1' => $sl_umur,
                    'bulan1' => $sl_bulan,
                    'umur_bulan1' => $total_bulan,
                    'Umur2' => $sl_umur1 ,
                    'bulan2' =>  $sl_bulan1,
                    'umur_bulan2' =>  $total_bulan1,
                    'Syarat_Kecacatan' =>  $oku,
                    'syarat_3M' =>  $sl_3m,
                    'fasa' => $st_fasa,
                    'updated_at' => now()
                ]);
        }
        else
        {
            $insert_syarat_lain = DB::connection('emas')
            ->table('syarat_lain')
            ->insert(
                [
                    'Programkod' => $st_id ,
                    'Kategori' => 'SPM' ,
                    'Syarat_taraf_perkahwinan' => $kahwin ,
                    'Taraf_perkahwinan' => $sl_kahwin,
                    'Syarat_Jantina' => $jantina,
                    'Jantina' => $sl_jantina,
                    'Syarat_Umur' => $opumur,
                    'Operasi_Umur' => $sl_opumur,
                    'Umur1' => $sl_umur,
                    'bulan1' => $sl_bulan,
                    'umur_bulan1' => $total_bulan,
                    'Umur2' => $sl_umur1 ,
                    'bulan2' =>  $sl_bulan1,
                    'umur_bulan2' =>  $total_bulan1,
                    'Syarat_Kecacatan' =>  $oku,
                    'syarat_3M' =>  $sl_3m,
                    'sesi' =>  session()->get('sesi_semasa'),
                    'fasa' => $st_fasa,
                    'updated_at' => now()
                ]);
        }

        $update_catatan = DB::connection('emas')
        ->table('program')
        ->where('KODPROGRAM', $st_id)
        ->where('LEPASAN','SPM')
        ->where('SESI', session()->get('sesi_semasa'))
        ->update(
            [
                'REMARKS' => trim($st_catat)
            ]);

        return redirect('/esyaratkhas?page=1')->with('success', 'Syarat Khas bagi Program ' .$st_id. ' telah dikemaskini.');
    }

    public function salin(Request $request)
    {
        // PERLU TUKAR MENGIKUT KESESUAIAN UNTUK KEMASKINI
        // FLAG UNTUK FASA KEMASKINI
        // 1 = FASA TAFSIRAN 2 = BUKAN FASA TAFSIRAN
        $fasa_kemaskini = '1';

        // REQUEST HIDDEN FORM
        $st_fasa = $fasa_kemaskini;
		$id = $request->input('id');
        $kodprogram = $request->input('kodprogram');
        $kategori = $request->input('kategori');
        $salin_kod1 = $request->input('SALIN_SYARAT1');
        $salin_kod2 = $request->input('SALIN_SYARAT2');
        $salin_kod3 = $request->input('SALIN_SYARAT3');
        // $salin_kod4 = $request->input('SALIN_SYARAT4');
        // $salin_kod5 = $request->input('SALIN_SYARAT5');

        $sesi_semasa = session()->get('sesi_semasa');

        if($salin_kod1!='') { include(app_path() . '/Http/Controllers/es_syarat/spm/pakej_salin/salin_1.php'); }
        if($salin_kod2!='') { include(app_path() . '/Http/Controllers/es_syarat/spm/pakej_salin/salin_2.php'); }
        if($salin_kod3!='') { include(app_path() . '/Http/Controllers/es_syarat/spm/pakej_salin/salin_3.php'); }
        // if($salin_kod4!='') { include(app_path() . '/Http/Controllers/es_syarat/spm/pakej_salin/salin_4.php'); }
        // if($salin_kod5!='') { include(app_path() . '/Http/Controllers/es_syarat/spm/pakej_salin/salin_5.php'); }

		if($salin_kod1!='') { $kodprogram_salin = [$salin_kod1];}
		if($salin_kod2!='') { $kodprogram_salin = [$salin_kod2];}
		if($salin_kod3!='') { $kodprogram_salin = [$salin_kod3];}
		if($salin_kod1!='' && $salin_kod2!='') { $kodprogram_salin = [$salin_kod1,$salin_kod2];}
		if($salin_kod1!='' && $salin_kod3!='') { $kodprogram_salin = [$salin_kod1,$salin_kod3];}
		if($salin_kod2!='' && $salin_kod3!='') { $kodprogram_salin = [$salin_kod2,$salin_kod3];}
		if($salin_kod1!='' && $salin_kod2!=''  && $salin_kod3!='') { $kodprogram_salin = [$salin_kod1,$salin_kod2,$salin_kod3];}

		$message=join(' , ',$kodprogram_salin);
        return redirect()->back()->with('success', 'Syarat bagi program ' .$id. ' telah berjaya disalin ke ' .$message );
    }

    public function prnpreview()
    {
        $codeset_oku = DB::connection('upucodeset')->table('refperibadi_jencacat')->get();
        $skgred = DB::connection('upucodeset')->table('refspm_gred')->whereNotIn('kodspmgred',['R','T','X','Z'])->get();

        if(session()->get('kategori') =='spmA') { $kat= ['A']; }
        elseif(session()->get('kategori') =='spmB') { $kat= ['B']; }
        else { $kat= ['A','B']; }

        $sessionSesi = session()->get('sesi_semasa');
        include(app_path() . '/Http/Controllers/es_syarat/spm/include_cetak_syarat/upuplus_program.php');

        $sessionIpta = session()->get('ipta');
        if (in_array(Auth::user()->ipta, ['11', '22'])) {
            $iptaKod = $sessionIpta != '' ? $sessionIpta : 'UA';
        } elseif (Auth::user()->ipta == 'FA' && $sessionIpta == '') {
            $iptaKod = 'FB';
        } elseif (Auth::user()->ipta == 'OI' && $sessionIpta == '') {
            $iptaKod = 'OI';
        } elseif (Auth::user()->ipta == 'OP' && $sessionIpta == '') {
            $iptaKod = 'OP';
        } elseif (Auth::user()->ipta == 'IA' && $sessionIpta == '') {
            $iptaKod = 'IA';
        } else {
            $iptaKod = Auth::user()->ipta;
        }

        $codeset_ipta = DB::connection('emas')->table('upu_ipta2')->where('IPTA_KOD', $iptaKod)->first();

        return view('es_syaratkhas.spm.cetak', compact('cetak','codeset_ipta','codeset_oku'));
    }

    public function cetak_oku()
    {
        $codeset_oku = DB::connection('upucodeset')->table('refperibadi_jencacat')->get();

        $cetak_oku = DB::connection('emas')->table('program')
        ->where('LEPASAN',session()->get('login_jenprog'))
        ->where('SESI',session()->get('sesi_semasa'))
        ->where('STATUS_TAWAR','Y')
        ->when((Auth::user()->ipta=='11' || Auth::user()->ipta=='22' || Auth::user()->ipta=='FA' || Auth::user()->ipta=='OI' || Auth::user()->ipta=='OP' || Auth::user()->ipta=='IA') && session()->get('ipta') != '', function ($q) {
            return $q->where(DB::raw('substr(KODPROGRAM_PAPAR, 1, 2)'), 'like', '%'.session()->get('ipta').'%')
                    ->where('KODPROGRAM_PAPAR', 'like', '%'.session()->get('program').'%');
        })
        ->when((Auth::user()->ipta=='11' || Auth::user()->ipta=='22') && session()->get('ipta') == '', function ($q) {
            return $q->where(DB::raw('substr(KODPROGRAM_PAPAR, 1, 2)'), 'UA')
                    ->where('KODPROGRAM_PAPAR', 'like', '%'.session()->get('program').'%');
        })
        ->when((Auth::user()->ipta=='FA') && session()->get('ipta') == '', function ($q) {
            return $q->where(DB::raw('substr(KODPROGRAM_PAPAR, 1, 2)'), 'FB')
                    ->where('KODPROGRAM_PAPAR', 'like', '%'.session()->get('program').'%');
        })
        ->when((Auth::user()->ipta=='OI') && session()->get('ipta') == '', function ($q) {
            return $q->where(DB::raw('substr(KODPROGRAM_PAPAR, 1, 2)'), 'OI')
                    ->where('KODPROGRAM_PAPAR', 'like', '%'.session()->get('program').'%');
        })
        ->when((Auth::user()->ipta=='OP') && session()->get('ipta') == '', function ($q) {
            return $q->where(DB::raw('substr(KODPROGRAM_PAPAR, 1, 2)'), 'OP')
                    ->where('KODPROGRAM_PAPAR', 'like', '%'.session()->get('program').'%');
        })
        ->when((Auth::user()->ipta=='IA') && session()->get('ipta') == '', function ($q) {
            return $q->where(DB::raw('substr(KODPROGRAM_PAPAR, 1, 2)'), 'IA')
                    ->where('KODPROGRAM_PAPAR', 'like', '%'.session()->get('program').'%');
        })
        ->when((Auth::user()->ipta!='11' && Auth::user()->ipta!='22' && Auth::user()->ipta!='FA' && Auth::user()->ipta!='OI' && Auth::user()->ipta!='OP' && Auth::user()->ipta!='IA'), function ($q) {
            return $q->where(DB::raw('substr(KODPROGRAM_PAPAR, 1, 2)'), Auth::user()->ipta)
                    ->where('KODPROGRAM_PAPAR', 'like', '%'.session()->get('program').'%');
        })
        ->groupby('KODPROGRAM_PAPAR')
        ->orderby('KODPROGRAM_PAPAR','ASC')
        ->get();

        $sessionSesi = session()->get('sesi_semasa');
        include(app_path() . '/Http/Controllers/es_syarat/spm/include_cetak_syarat/upuplus_jenis_oku.php');

        $list_oku = DB::connection('emas')
        ->table('upuplus_jenis_oku')
        ->where('jenprog',session()->get('login_jenprog'))
        ->where('SESI',session()->get('sesi_semasa'))
        ->groupBy('kodprogram','kod_oku')
        ->get();

        $sessionIpta = session()->get('ipta');
        if (in_array(Auth::user()->ipta, ['11', '22'])) {
            $iptaKod = $sessionIpta != '' ? $sessionIpta : 'UA';
        } elseif (Auth::user()->ipta == 'FA' && $sessionIpta == '') {
            $iptaKod = 'FB';
        } elseif (Auth::user()->ipta == 'OI' && $sessionIpta == '') {
            $iptaKod = 'OI';
        } elseif (Auth::user()->ipta == 'OP' && $sessionIpta == '') {
            $iptaKod = 'OP';
        } elseif (Auth::user()->ipta == 'IA' && $sessionIpta == '') {
            $iptaKod = 'IA';
        } else {
            $iptaKod = Auth::user()->ipta;
        }

        $codeset_ipta = DB::connection('emas')->table('upu_ipta2')->where('IPTA_KOD', $iptaKod)->first();

        $pdf = PDF::loadView('es_syaratkhas.spm.cetak_oku',['cetak_oku' => $cetak_oku, 'list_oku' => $list_oku, 'codeset_ipta' => $codeset_ipta, 'codeset_oku' => $codeset_oku]);
        return $pdf->stream('SENARAI PROGRAM VS ORANG KURANG UPAYA (OKU).pdf');
    }

    public function modal_salin(Request $request,$idsyarat)
    {
        $modal_syarat = DB::connection('emas')->table('program')
        ->where('SESI',session()->get('sesi_semasa'))
        ->where('KODPROGRAM', $idsyarat)
        ->where('LEPASAN', session()->get('login_jenprog'))
        ->get();

        $kod_program= DB::connection('emas')->table('program')
        ->when((Auth::user()->ipta=='11' || Auth::user()->ipta=='22' || Auth::user()->ipta=='FA' || Auth::user()->ipta=='OI' || Auth::user()->ipta=='OP' || Auth::user()->ipta=='IA') && session()->get('ipta') != '', function ($q) {
            return $q->where(DB::raw('substr(KODPROGRAM_PAPAR, 1, 2)'), 'like', '%'.session()->get('ipta').'%');
        })
        ->when((Auth::user()->ipta=='11' || Auth::user()->ipta=='22') && session()->get('ipta') == '', function ($q) {
            return $q->where(DB::raw('substr(KODPROGRAM_PAPAR, 1, 2)'), 'UA');
        })
        ->when((Auth::user()->ipta=='FA') && session()->get('ipta') == '', function ($q) {
            return $q->where(DB::raw('substr(KODPROGRAM_PAPAR, 1, 2)'), 'FB');
        })
        ->when((Auth::user()->ipta=='OI') && session()->get('ipta') == '', function ($q) {
            return $q->where(DB::raw('substr(KODPROGRAM_PAPAR, 1, 2)'), 'OI');
        })
        ->when((Auth::user()->ipta=='OP') && session()->get('ipta') == '', function ($q) {
            return $q->where(DB::raw('substr(KODPROGRAM_PAPAR, 1, 2)'), 'OP');
        })
        ->when((Auth::user()->ipta=='IA') && session()->get('ipta') == '', function ($q) {
            return $q->where(DB::raw('substr(KODPROGRAM_PAPAR, 1, 2)'), 'IA');
        })
        ->where('LEPASAN',session()->get('login_jenprog'))
        ->where('SESI',session()->get('sesi_semasa'))
        ->where('STATUS_TAWAR','Y')
        ->orderby('KODPROGRAM','ASC')
        ->get();

        return view('es_syaratkhas.spm.salin',compact('modal_syarat','kod_program'));

    }

}
