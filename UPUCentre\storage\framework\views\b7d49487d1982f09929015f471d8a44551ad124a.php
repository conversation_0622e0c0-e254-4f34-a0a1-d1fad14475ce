<?php if(!empty($mpb)): ?>
<div class="col-xl-8 col-lg-8 mb-3" id="watermark_mohon">
    <div class="alert alert-secondary bg-light" role="alert">

        <?php $__currentLoopData = $mpb; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $fasa_mpb): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <h4><?php echo e(trim($fasa_mpb->NAMA)); ?></h4>
            <h5><?php echo e(trim($fasa_mpb->NOKP)); ?> <sup>(


                
                 <?php echo e(trim($fasa_mpb->KATAG)); ?>

                
                <?php if(in_array($fasa_mpb->KATAG, ['E', 'F', 'G'])): ?>
                    /<?php echo e(trim($fasa_mpb->JENSETARAF)); ?>

                    
                    <?php $found = false; ?>
                    <?php $__currentLoopData = $jensetaraf; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $jen_setaraf): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php if($fasa_mpb->JENSETARAF == $jen_setaraf->kodjensetaraf): ?>
                            - <?php echo e($jen_setaraf->ketjensetaraf); ?>

                            <?php $found = true; ?>
                            <?php break; ?>
                        <?php endif; ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                    
                    <?php if(!$found): ?>
                        <?php switch($fasa_mpb->JENSETARAF):
                            case ('E1'): ?> - DKM / DLKM <?php break; ?>
                            <?php case ('E2'): ?> - DVM <?php break; ?>
                            <?php case ('E3'): ?> - DIPLOMA ILKA / IPTS / LUAR NEGARA / LAIN-LAIN DIPLOMA <?php break; ?>
                            <?php case ('E4'): ?> - DIPLOMA ILKA <?php break; ?>
                            <?php case ('F1'): ?> - A-LEVEL <?php break; ?>
                            <?php case ('F2'): ?> - IB <?php break; ?>
                            <?php case ('F3'): ?> - SEKOLAH SUKAN (SSB) / SSTMI / SSMP <?php break; ?>
                            <?php case ('F4'): ?> - STPM BUKAN TAHUN SEMASA <?php break; ?>
                            <?php case ('F5'): ?> - MATRIKULASI KPM / ASASI (BUKAN TAHUN SEMASA)<?php break; ?>
                            <?php case ('F6'): ?> - STAM BUKAN TAHUN SEMASA <?php break; ?>
                            <?php case ('F7'): ?> - AUSMAT / SAM / ADFP <?php break; ?>
                            <?php case ('F8'): ?> - ASASI IPTS <?php break; ?>
                            <?php case ('F9'): ?> - UEC (SENIOR MIDDLE THREE)<?php break; ?>
                            <?php case ('G1'): ?> - DIPLOMA UA <?php break; ?>
                            <?php case ('G2'): ?> - DIPLOMA POLITEKNIK <?php break; ?>
                        <?php endswitch; ?>
                    <?php endif; ?>
                <?php else: ?>
                    
                    <?php switch($fasa_mpb->KATAG):
                        case ('A'): ?>
                            <?php if(request()->input('JENPROG')=='spm'): ?>
                                - KATEGORI A
                            <?php else: ?>
                                - STPM SASTERA
                            <?php endif; ?>
                            <?php break; ?>
                        <?php case ('B'): ?>
                            <?php if(request()->input('JENPROG')=='spm'): ?>
                                - KATEGORI B
                            <?php else: ?>
                                - KATEGORI B
                            <?php endif; ?>
                            <?php break; ?>
                        <?php case ('S'): ?>
                            <?php if(request()->input('JENPROG')=='stpm'): ?>
                                - STPM SAINS
                            <?php else: ?>
                                - STPM (SAINS)
                            <?php endif; ?>
                            <?php break; ?>
                        <?php case ('P'): ?> - MATRIKULASI (AKAUN)<?php break; ?>
                        <?php case ('L'): ?> - ASASI (TESL)<?php break; ?>
                        <?php case ('U'): ?> - ASASI (UNDANG-UNDANG)<?php break; ?>
                        <?php case ('N'): ?> - MATRIKULASI (SAINS)<?php break; ?>
                        <?php case ('K'): ?> - ASASI (KEJURUTERAAN)<?php break; ?>
                        <?php case ('J'): ?> - MATRIKULASI (TEKNIKAL)<?php break; ?>
                        <?php case ('M'): ?> - ASASI (SOSIAL)<?php break; ?>
                        <?php case ('V'): ?> - ASASI (TVET)<?php break; ?>
                        <?php case ('T'): ?> - STAM <?php break; ?>
                    <?php endswitch; ?>
                <?php endif; ?>



                )<span style="font-family: consolas;font-size:small" class="text-muted">[<?php echo e((int)trim($fasa_mpb->MERIT)); ?>]</span></sup></h5>
            <span class="text-primary">(SESI 20<?php echo e(substr(request()->input('SESIAKAD'),0,2)); ?> / 20<?php echo e(substr(request()->input('SESIAKAD'),2,2)); ?>)</span class="text-primary">
            <hr>

            <?php if(request()->input('SESIAKAD') > '2223'): ?>
            <button type="button" class="btn btn-primary btn-sm mb-3" data-nokp="<?php echo e(request()->input('NOKP')); ?>" data-toggle="modal" data-target="#mpb_<?php echo e(request()->input('NOKP')); ?>" data-keyboard="false" data-backdrop="static">
                <i class="fas fa-fw fa-info-circle"></i> Maklumat Terperinci
            </button>
            <?php endif; ?>

            
            

            <?php if(Auth::user()->roles=='1' || Auth::user()->roles=='2'): ?>
                <?php $__currentLoopData = $nama_pengguna; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $upu_user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php if(Auth::user()->user_id == $upu_user->user_id): ?>
                    <div class="form-row">
                        <div class="form-group col-md-12">
                            <h6 class="border-bottom badge-line"><label for="mohon" class="mb-n2 badge badge-info rounded-sm">Jenis Prajaya</label></h6>
                                <?php $__currentLoopData = $jen_pjaya; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $pjaya): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php if($pjaya->kodjenpjaya==$fasa_mpb->JEN_PJAYA): ?>
                                        <?php echo e($pjaya->ketjenpjaya); ?>

                                    <?php endif; ?>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                                <?php if($fasa_mpb->KURJAYA=='' && $fasa_mpb->KURJAYA2==''): ?>
                                    -----------------------
                                <?php endif; ?>

                        </div>
                    </div>
                    <?php endif; ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php endif; ?>

            <div class="form-row">
                <div class="form-group col-md-12">
                    <h6 class="border-bottom badge-line"><label for="mohon" class="mb-n2 badge badge-info rounded-sm">Program Jaya</label></h6>
                        <?php
                        if(request()->input('JENPROG')=='spm')
                        {
                            $kursus_sql  = \DB::connection('mysql_online')->select(DB::raw("SELECT * FROM 02_all_kodspm"));
                        }
                        elseif(request()->input('JENPROG')=='stpm')
                        {
                            $kursus_sql  = \DB::connection('mysql_online')->select(DB::raw("SELECT * FROM 01_all_kodstpm"));
                        }
                        ?>

                        <?php $__currentLoopData = $kursus_sql; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $kurjaya): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php if($kurjaya->kod==$fasa_mpb->KURJAYA): ?>
                            <b><?php echo e($kurjaya->kod); ?> - <?php echo e($kurjaya->ipta); ?> <?php echo e($kurjaya->program); ?> <?php if($kurjaya->tduga=='Y'): ?> # <?php endif; ?></b>
                            <br><span class="badge mt-1" style="background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7;">
                                <?php $__currentLoopData = $jen_pjaya; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $pjaya): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php if($pjaya->kodjenpjaya==$fasa_mpb->JEN_PJAYA): ?>
                                        
                                        <?php echo e($pjaya->ketjenpjaya); ?>

                                    <?php endif; ?>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </span>
                            <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>



                        <?php if($fasa_mpb->KURJAYA==''): ?>
                        <?php if(trim($fasa_mpb->SBBXLYK) != ''): ?>
                        <span class="text-danger font-weight-bold">
                        <?php echo e($fasa_mpb->SBBXLYK); ?> -
                        <?php $__currentLoopData = $sbbxlayak; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sbb_xlayak): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php if(trim($fasa_mpb->SBBXLYK) == trim($sbb_xlayak->kodsbbxlyk)): ?>
                        <?php echo e($sbb_xlayak->ketsbbxlyk); ?>

                        <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </span>
                        <?php else: ?>
                        Tiada Tawaran
                        <?php endif; ?>
                        <?php endif; ?>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group col-md-12">
                    <h6 class="border-bottom badge-line"><label for="mohon" class="mb-n2 badge badge-info rounded-sm">Lain-lain Kurjaya</label></h6>
                        <?php if(request()->input('JENPROG')=='spm'): ?>
                         <?php if(TRIM($fasa_mpb->KURJAYA2!='')): ?><?php echo e($fasa_mpb->KURJAYA2); ?> <?php else: ?> TIADA <?php endif; ?>
                        <?php else: ?>
                        <?php if(TRIM($fasa_mpb->KURJAYA2!='')): ?><?php echo e($fasa_mpb->KURJAYA2); ?> <?php else: ?> TIADA <?php endif; ?>
                        <?php endif; ?>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group col-md-12">
                    <h6 class="border-bottom badge-line"><label for="mohon" class="mb-n2 badge badge-info rounded-sm">Tajaan</label></h6>
                        <?php if(request()->input('JENPROG')=='spm'): ?>
                         <?php if(TRIM($fasa_mpb->KURJAYA3!='')): ?><?php echo e($fasa_mpb->KURJAYA3); ?> <?php else: ?> TIADA <?php endif; ?>
                        <?php else: ?>
                        <?php if(TRIM($fasa_mpb->KURJAYA3!='')): ?><?php echo e($fasa_mpb->KURJAYA3); ?> <?php else: ?> TIADA <?php endif; ?>
                        <?php endif; ?>
                </div>
            </div>


        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

        <div class="table-responsive">
            <table class="table table-sm table-bordered tbl_font" id="tblprofil">
                <thead class="thead-dark">
                    <tr>
                        <th colspan="10">Pilihan Program</th>
                    </tr>
                </thead>
                <thead>
                    <tr>
                        <th style="width:50px; vertical-align:middle; text-align:center; font-weight:bold;">Pilihan</th>
                        <th style="width:80px; font-weight:bold; vertical-align:middle; text-align:center;">Institusi / <br> Agensi</th>
                        <th style="width:100px; font-weight:bold; vertical-align:middle; text-align:center;">Kod</th>
                        <th style="font-weight:bold; vertical-align:middle;">Program<br /><strong></strong></th>

                        

                        <?php if(request()->input('SESIAKAD') == '2526' || request()->input('SESIAKAD') == '2526'): ?>
                        <th hidden style="width:80px; font-weight:bold; vertical-align:middle; text-align:center;">Purata <br> Markah <br> Merit</th>
                        <?php endif; ?>

                        <th style="width:80px; font-weight:bold; vertical-align:middle; text-align:center;">Lulus Syarat Khas</th>

                        
            <?php if(in_array(request()->input('SESIAKAD'), ['2526'])): ?>
                        <th style="width:80px; font-weight:bold; vertical-align:middle; text-align:center;">Panggilan Tduga</th>
                        <th style="width:80px; font-weight:bold; vertical-align:middle; text-align:center;">Peraku <br> Tduga</th>
                        <?php endif; ?>
                    </tr>
                </thead>


                <tbody>
                <?php $__currentLoopData = $mpb; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $fasa_mpb): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>

                <?php
                    if(request()->input('JENPROG')=='spm'){$pilLimit=12;}
                    if(request()->input('JENPROG')=='stpm' && ($fasa_mpb->KATAG!='G' && $fasa_mpb->KATAG!='E' && $fasa_mpb->KATAG!='F')){$pilLimit=12;}
                    if(request()->input('JENPROG')=='stpm' && ($fasa_mpb->KATAG=='G' || $fasa_mpb->KATAG=='E' || $fasa_mpb->KATAG=='F')){$pilLimit=8;}
                ?>



                    <?php for($p=1;$p<=$pilLimit;$p++): ?>

                        <?php if(request()->input('JENPROG')=='spm'): ?>
                        <tr <?php if(substr($fasa_mpb->KURJAYA,0,1)=='U'): ?> <?php if($fasa_mpb->{'PIL'.$p}==$fasa_mpb->KURJAYA && trim($fasa_mpb->KURJAYA) <> ''): ?> class="text-success font-weight-bold" style="font-size:0.9rem;" <?php endif; ?> <?php else: ?> <?php if($fasa_mpb->{'PIL'.$p}==$fasa_mpb->KURJAYA && $fasa_mpb->{'PPIL'.$p}==$fasa_mpb->KURJAYAPUSAT && trim($fasa_mpb->KURJAYA) <> ''): ?> class="text-success font-weight-bold" style="font-size:0.9rem;" <?php endif; ?> <?php endif; ?> <?php if(trim($fasa_mpb->{'LAY'.$p})!=''): ?> class="text-danger"<?php endif; ?>>
                        <?php endif; ?>

                        <?php if(request()->input('JENPROG')=='stpm'): ?>
                        <tr <?php if(substr($fasa_mpb->KURJAYA,0,1)=='U'): ?> <?php if($fasa_mpb->{'PIL'.$p}==$fasa_mpb->KURJAYA && trim($fasa_mpb->KURJAYA) <> ''): ?> class="text-success font-weight-bold" style="font-size:0.9rem;" <?php endif; ?> <?php endif; ?> <?php if(trim($fasa_mpb->{'LAY'.$p})!=''): ?> class="text-danger"<?php endif; ?>>
                        <?php endif; ?>

                            <td style="vertical-align:middle; text-align:center;"><?php echo e($p); ?></td>
                            <td style="vertical-align:middle; text-align:center;">
                                <?php
                                    if(request()->input('JENPROG')=='spm')
                                    {
                                    $kursus_sql  = \DB::connection('mysql_online')->select(DB::raw("SELECT ipta FROM 02_all_kodspm WHERE kod='".$fasa_mpb->{'PIL'.$p}."'"));
                                    }
                                    elseif(request()->input('JENPROG')=='stpm')
                                    {
                                        $kursus_sql  = \DB::connection('mysql_online')->select(DB::raw("SELECT ipta FROM 01_all_kodstpm WHERE kod='".$fasa_mpb->{'PIL'.$p}."'"));
                                    }
                                ?>

                                <?php $__currentLoopData = $kursus_sql; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $kursus_row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php echo e($kursus_row->ipta); ?>

                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </td>
                            <td style="vertical-align:middle; text-align:center;">
                                <?php echo e($fasa_mpb->{'PIL'.$p}); ?>

                                <?php if(trim($fasa_mpb->{'PIL'.$p}) != ''): ?>
                                    <br>
                                    <?php if(request()->input('JENPROG')=='stpm'): ?>
                                    <button type="button" class="btn btn-info btn-sm mt-1"
                                            onclick="showSyaratTest('<?php echo e($fasa_mpb->{'PIL'.$p}); ?>', '<?php echo e($fasa_mpb->KATAG); ?>'<?php if(in_array($fasa_mpb->KATAG, ['E', 'F', 'G'])): ?>, '<?php echo e($fasa_mpb->JENSETARAF); ?>'<?php endif; ?>)"
                                            title="Lihat Kelayakan Minimum Program">
                                        <i class="fas fa-list-alt"></i> Syarat
                                    </button>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php
                                    if(request()->input('JENPROG')=='spm')
                                    {
                                        $kursus_sql  = \DB::connection('mysql_online')->select(DB::raw("SELECT program,tduga FROM 02_all_kodspm WHERE kod='".$fasa_mpb->{'PIL'.$p}."'"));
                                        $pstlatihan_sql  = \DB::connection('mysql_online')->select(DB::raw("SELECT pusatlatihan FROM 02_pusat_latihan WHERE kodkursusbaru='".$fasa_mpb->{'PIL'.$p}."' AND kodpusatlatihan='".$fasa_mpb->{'PPIL'.$p}."'"));
                                    }
                                    elseif(request()->input('JENPROG')=='stpm')
                                    {
                                        $kursus_sql  = \DB::connection('mysql_online')->select(DB::raw("SELECT program,tduga FROM 01_all_kodstpm WHERE kod='".$fasa_mpb->{'PIL'.$p}."'"));
                                    }
                                ?>

                                <?php $__currentLoopData = $kursus_sql; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $kursus_row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php echo e($kursus_row->program); ?> <?php if($kursus_row->tduga=='Y'): ?> # <?php endif; ?>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>


                                
                                <?php if(request()->input('JENPROG')=='stpm' && request()->input('SESIAKAD') != '' && TRIM($fasa_mpb->{'PIL'.$p}) != ''): ?>
                                    <?php
                                        // Get session value from dropdown selection
                                        $sesiakad = request()->input('SESIAKAD');

                                        // Map session to table name
                                        if(request()->input('JENPROG')=='stpm') {
                                            if($sesiakad=='2122') {
                                                $tableName = '2122_stpm_utama';
                                            }
                                            elseif($sesiakad=='2223') {
                                                $tableName = '2223_stpm_utama';
                                            }
                                            elseif($sesiakad=='2324') {
                                                $tableName = '2324_stpm_utama';
                                            }
                                            elseif($sesiakad=='2425') {
                                                $tableName = '2425_stpm_utama';
                                            }
                                            elseif($sesiakad=='2526') {
                                                $tableName = '2526_stpm_utama';
                                            }
                                            else {
                                                $tableName = '2526_stpm_utama'; // fallback
                                            }
                                        }

                                        // Determine JEN_PJAYA filter based on KATAG
                                        if(in_array($fasa_mpb->KATAG, ['E', 'F', 'G'])) {
                                            $jenPjayaFilter = "JEN_PJAYA = '9'";
                                            $labelType = "Diploma Setaraf";
                                        } else {
                                            $jenPjayaFilter = "JEN_PJAYA IN('0','1')";
                                            $labelType = "Prajaya";
                                        }

                                        // Get PNGK range for this program and student category
                                        [$pngkMax, $pngkMin] = \Cache::remember('pngk_range_'.$fasa_mpb->{'PIL'.$p}.'_'.$fasa_mpb->KATAG.'_'.$sesiakad, 22800, function () use ($fasa_mpb, $p, $tableName, $jenPjayaFilter) {
                                            // Get MAX PURATAPNGK with CAST to ensure numeric comparison
                                            $maxResult = \DB::connection('upu_cloud')->select(\DB::raw("SELECT MAX(CAST(PURATAPNGK AS DECIMAL(5,2))) as max_pngk FROM {$tableName} WHERE KURJAYA = ? AND {$jenPjayaFilter} AND PURATAPNGK IS NOT NULL AND PURATAPNGK != ''"), [$fasa_mpb->{'PIL'.$p}]);

                                            // Get MIN PURATAPNGK with CAST to ensure numeric comparison
                                            $minResult = \DB::connection('upu_cloud')->select(\DB::raw("SELECT MIN(CAST(PURATAPNGK AS DECIMAL(5,2))) as min_pngk FROM {$tableName} WHERE KURJAYA = ? AND {$jenPjayaFilter} AND PURATAPNGK IS NOT NULL AND PURATAPNGK != ''"), [$fasa_mpb->{'PIL'.$p}]);

                                            return [
                                                isset($maxResult[0]) ? $maxResult[0]->max_pngk : null, // max PNGK
                                                isset($minResult[0]) ? $minResult[0]->min_pngk : null // min PNGK
                                            ];
                                        });

                                        // // Get MARKOKO range for this program and student category
                                        // [$markokoMax, $markokoMin] = \Cache::remember('markoko_range_v2_'.$fasa_mpb->{'PIL'.$p}.'_'.$fasa_mpb->KATAG.'_'.$sesiakad, 22800, function () use ($fasa_mpb, $p, $tableName, $jenPjayaFilter) {
                                        //     // Get MAX MARKOKO with CAST and TRIM to ensure numeric comparison
                                        //     $maxResult = \DB::connection('upu_cloud')->select(\DB::raw("SELECT MAX(CAST(TRIM(MARKOKO) AS DECIMAL(10,2))) as max_markoko FROM {$tableName} WHERE KURJAYA = ? AND {$jenPjayaFilter} AND MARKOKO IS NOT NULL AND TRIM(MARKOKO) != '' AND TRIM(MARKOKO) != '0'"), [$fasa_mpb->{'PIL'.$p}]);

                                        //     // Get MIN MARKOKO with CAST and TRIM to ensure numeric comparison
                                        //     $minResult = \DB::connection('upu_cloud')->select(\DB::raw("SELECT MIN(CAST(TRIM(MARKOKO) AS DECIMAL(10,2))) as min_markoko FROM {$tableName} WHERE KURJAYA = ? AND {$jenPjayaFilter} AND MARKOKO IS NOT NULL AND TRIM(MARKOKO) != '' AND TRIM(MARKOKO) != '0'"), [$fasa_mpb->{'PIL'.$p}]);

                                        //     return [
                                        //         isset($maxResult[0]) ? $maxResult[0]->max_markoko : null, // max MARKOKO
                                        //         isset($minResult[0]) ? $minResult[0]->min_markoko : null // min MARKOKO
                                        //     ];
                                        // });
                                        //
                                        // Get MARKOKO range for this program and student category (exclude E, F, G)
                                        if(!in_array($fasa_mpb->KATAG, ['E', 'F', 'G'])) {
                                            [$markokoMax, $markokoMin] = \Cache::remember('markoko_range_v2_'.$fasa_mpb->{'PIL'.$p}.'_'.$fasa_mpb->KATAG.'_'.$sesiakad, 22800, function () use ($fasa_mpb, $p, $tableName, $jenPjayaFilter) {
                                                // Get MAX MARKOKO with CAST and TRIM to ensure numeric comparison
                                                $maxResult = \DB::connection('upu_cloud')->select(\DB::raw("SELECT MAX(CAST(TRIM(MARKOKO) AS DECIMAL(10,2))) as max_markoko FROM {$tableName} WHERE KURJAYA = ? AND {$jenPjayaFilter} AND MARKOKO IS NOT NULL AND TRIM(MARKOKO) != '' AND TRIM(MARKOKO) != '0'"), [$fasa_mpb->{'PIL'.$p}]);

                                                // Get MIN MARKOKO with CAST and TRIM to ensure numeric comparison
                                                $minResult = \DB::connection('upu_cloud')->select(\DB::raw("SELECT MIN(CAST(TRIM(MARKOKO) AS DECIMAL(10,2))) as min_markoko FROM {$tableName} WHERE KURJAYA = ? AND {$jenPjayaFilter} AND MARKOKO IS NOT NULL AND TRIM(MARKOKO) != '' AND TRIM(MARKOKO) != '0'"), [$fasa_mpb->{'PIL'.$p}]);

                                                return [
                                                    isset($maxResult[0]) ? $maxResult[0]->max_markoko : null, // max MARKOKO
                                                    isset($minResult[0]) ? $minResult[0]->min_markoko : null // min MARKOKO
                                                ];
                                            });
                                        } else {
                                            // Set null values for E, F, G categories
                                            $markokoMax = null;
                                            $markokoMin = null;
                                        }

                                    ?>

                                    <?php if($pngkMax && $pngkMin): ?>
                                        <br><span class="badge mt-1" style="background-color: #e3f2fd; color: #1565c0; border: 1px solid #90caf9; font-size: 0.75rem;">
                                            PNGK <?php echo e($labelType); ?>: <?php echo e($pngkMin); ?> - <?php echo e($pngkMax); ?>

                                        </span>
                                    <?php else: ?>
                                        
                                        <br><small class="text-muted" style="font-size: 0.7rem;">[No PNGK data for <?php echo e($labelType); ?>]</small>
                                    <?php endif; ?>

                                    <?php if($markokoMax && $markokoMin): ?>
                                        <br><span class="badge mt-1" style="background-color: #e0f2f1; color: #00695c; border: 1px solid #4db6ac; font-size: 0.75rem;">
                                            MARKOKO <?php echo e($labelType); ?>: <?php echo e($markokoMin); ?> - <?php echo e($markokoMax); ?>

                                        </span>
                                    <?php else: ?>
                                        
                                        <br><small class="text-muted" style="font-size: 0.7rem;">[Tiada data MARKOKO untuk <?php echo e($labelType); ?>]</small>
                                    <?php endif; ?>
                                <?php endif; ?>


                                <?php if(request()->input('JENPROG')=='spm'): ?>
                                <?php $__currentLoopData = $pstlatihan_sql; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $pstlatihan_row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div><b>Pusat Latihan</b> :
                                        <?php echo e($pstlatihan_row->pusatlatihan); ?>

                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php endif; ?><br />












<?php if(request()->input('JENPROG')=='stpm' && request()->input('SESIAKAD') != '' && TRIM($fasa_mpb->{'PIL'.$p}) != ''): ?>
    <?php
        // Get session value from dropdown selection
        $sesiakad = request()->input('SESIAKAD');

        // Map session to table name (same logic as controller)
        if($sesiakad=='2122') {
            $tableName = '2122_spm_utama';
        }
        elseif($sesiakad=='2223') {
            $tableName = '2223_spm_utama';
        }
        elseif($sesiakad=='2324') {
            $tableName = '2324_spm_utama';
        }
        elseif($sesiakad=='2425') {
            $tableName = '2425_spm_utama_profile';
        }
        elseif($sesiakad=='2526') {
            $tableName = '2526_stpm_utama';
        }
        else {
            $tableName = '2526_stpm_utama'; // fallback
        }

        // Existing merit range code for ETemuduga + COBOL
        [$merittop, $meritbottom] = \Cache::remember('merit_topbottom_jenpjaya01stpmperdana_'.$fasa_mpb->{'PIL'.$p}.$fasa_mpb->KODKAT.'_'.$sesiakad, 22800, function () use ($fasa_mpb, $p, $tableName) {
            $rows = \DB::connection('upu_cloud')->select(\DB::raw("SELECT MERIT FROM {$tableName} WHERE KURJAYA = ? AND KODKAT = ? AND (TRIM(JEN_PJAYA) IN ('0','1')) ORDER BY MERIT"), [$fasa_mpb->{'PIL'.$p}, $fasa_mpb->KODKAT]);

            $count = count($rows);

            return [
                $count > 0 ? $rows[0]->MERIT : null, // merit awal
                $count > 1 ? $rows[$count - 1]->MERIT : ($count > 0 ? $rows[0]->MERIT : null) // merit akhir
            ];
        });



        // NEW: Get max merit for student's specific KURJAYA and KODTARAF
        /*$maxMeritForStudentCategory = \Cache::remember('max_merit_category_'.$fasa_mpb->NOKP.'_'.$fasa_mpb->{'PIL'.$p}.'_'.$sesiakad, 22800, function () use ($fasa_mpb, $p, $tableName) {
            $result = \DB::connection('upu_cloud')->select(\DB::raw("
                SELECT MAX(MERIT) as max_merit
                FROM {$tableName}
                WHERE KURJAYA = ?
                AND KODTARAF = ?
                AND JEN_PJAYA IN('0','1')
            "), [$fasa_mpb->{'PIL'.$p}, $fasa_mpb->KODTARAF]);

            return $result[0]->max_merit ?? null;
        });*/
    ?>

    
    <?php echo '<small style="font-family: consolas; font-weight: bold" class="text-primary">['.$merittop.' <--> '.$meritbottom.']</small>'; ?>


    
    
<?php endif; ?>
</td>

                            <?php if(Auth::user()->roles=='1' || Auth::user()->roles=='2'): ?>
                            
                            
                            <?php endif; ?>

                            <?php if(request()->input('SESIAKAD') == '2425' || request()->input('SESIAKAD') == '2526'): ?>
                            <td hidden style="vertical-align:middle; text-align:center; color:blue;">
                                <?php
                                    if(request()->input('JENPROG')=='spm')
                                    {
                                        if(request()->input('SESIAKAD') == '2425'){
                                            $purata_merit  = \DB::connection('kpt')->select(DB::raw("SELECT KODTAWARAN,PURATAMERIT FROM 2425_upu_puratameritspm WHERE KODTAWARAN=?"), [$fasa_mpb->{'PIL'.$p}]);
                                        }
                                        elseif(request()->input('SESIAKAD') == '2526'){
                                            $purata_merit  = \DB::connection('kpt')->select(DB::raw("SELECT programtawaranupu AS KODTAWARAN, puratameritbyyear AS PURATAMERIT FROM vw_programpuratamerit_2526 WHERE programtawaranupu=?"), [$fasa_mpb->{'PIL'.$p}]);
                                        }
                                    }
                                    elseif(request()->input('JENPROG')=='stpm')
                                    {
                                        if($fasa_mpb->KATAG=='T')
                                        {
                                            $purata_merit  = \DB::connection('kpt')->select(DB::raw("SELECT programtawaranupu as KODTAWARAN, puratameritbyyear as PURATAMERIT FROM vw_programpuratamerit_2526 WHERE kategori='STAM' AND programtawaranupu='".$fasa_mpb->{'PIL'.$p}."'"));
                                        }
                                        elseif ($fasa_mpb->KATAG=='P' || $fasa_mpb->KATAG=='L' || $fasa_mpb->KATAG=='U' || $fasa_mpb->KATAG=='N' || $fasa_mpb->KATAG=='K' || $fasa_mpb->KATAG=='J' || $fasa_mpb->KATAG=='A' || $fasa_mpb->KATAG=='S' || $fasa_mpb->KATAG=='M' || $fasa_mpb->KATAG=='V')
                                        {
                                            $purata_merit  = \DB::connection('kpt')->select(DB::raw("SELECT programtawaranupu as KODTAWARAN, puratameritbyyear as PURATAMERIT FROM vw_programpuratamerit_2526 WHERE kategori='STPM/MATRIKULASI/ASASI' AND programtawaranupu='".$fasa_mpb->{'PIL'.$p}."'"));
                                        }
                                    }
                                ?>

                                <?php if(($fasa_mpb->KATAG!='E' && $fasa_mpb->KATAG!='F' && $fasa_mpb->KATAG!='G')): ?>
                                    
                                    <?php if(isset($purata_merit[0]) && $purata_merit[0]->KODTAWARAN == $fasa_mpb->{'PIL'.$p}): ?>
                                        <?php if($purata_merit[0]->PURATAMERIT!='0.00'): ?>
                                            <span style="color:blue;"><?php echo e($purata_merit[0]->PURATAMERIT); ?> % <span>
                                        <?php else: ?>
                                            <span style="color:green;"><span>Program <br> Baru</span>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <?php echo e('TIADA'); ?>

                                    <?php endif; ?>
                                <?php endif; ?>

                                <?php if(($fasa_mpb->KATAG=='E' || $fasa_mpb->KATAG=='F' || $fasa_mpb->KATAG=='G')): ?>
                                    <span>-<span>
                                <?php endif; ?>
                            </td>
                            <?php endif; ?>

                            <td style="vertical-align:middle; text-align:center;">
                             <?php if(trim($fasa_mpb->{'LAY'.$p})=='' && trim($fasa_mpb->{'PIL'.$p})<>''): ?> <i class="fa fa-fw fa-check text-success"></i>
                                <?php elseif(trim($fasa_mpb->{'LAY'.$p})=='*' && trim($fasa_mpb->{'PIL'.$p})<>''): ?> *
                                <?php elseif(trim($fasa_mpb->{'LAY'.$p})=='?' && trim($fasa_mpb->{'PIL'.$p})<>''): ?> ?
                                <?php elseif(trim($fasa_mpb->{'LAY'.$p})=='$' && trim($fasa_mpb->{'PIL'.$p})<>''): ?> $
                                <?php elseif(trim($fasa_mpb->{'LAY'.$p})=='%' && trim($fasa_mpb->{'PIL'.$p})<>''): ?> %
                                <?php else: ?> -
                            <?php endif; ?>

                            </td>

                            
                <?php if(in_array(request()->input('SESIAKAD'), ['2526'])): ?>
                            <td style="vertical-align:middle; text-align:center;">
                                <?php
                                    if(request()->input('JENPROG')=='spm')
                                    {
                                        $kursus_sql  = \DB::connection('mysql_online')->select(DB::raw("SELECT tduga FROM 02_all_kodspm WHERE kod='".$fasa_mpb->{'PIL'.$p}."'"));
                                        $tduga_sql  = \DB::connection('mysql_online')->select(DB::raw("SELECT PANGGIL FROM 700_xfiles_spm_tduga WHERE NOKP='".$fasa_mpb->NOKP."' AND KOD='".$fasa_mpb->{'PIL'.$p}."' GROUP BY NOKP,KOD"));


                                    }
                                    elseif(request()->input('JENPROG')=='stpm')
                                    {
                                        $kursus_sql  = \DB::connection('mysql_online')->select(DB::raw("SELECT tduga FROM 01_all_kodstpm WHERE kod='".$fasa_mpb->{'PIL'.$p}."'"));
                                        $tduga_sql  = \DB::connection('mysql_online')->select(DB::raw("SELECT PANGGIL FROM 700_xfiles_stpm_tduga WHERE NOKP='".$fasa_mpb->NOKP."' AND KOD='".$fasa_mpb->{'PIL'.$p}."' GROUP BY NOKP,KOD"));
                                    }
                                ?>

                                <?php $__currentLoopData = $kursus_sql; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $kursus_row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>

                                    <?php if($kursus_row->tduga=='Y'): ?>

                                        <?php $__empty_1 = true; $__currentLoopData = $tduga_sql; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tduga_row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                            <?php if($tduga_row->PANGGIL=='Y'): ?> <i class="fa fa-fw fa-check text-success"></i>
                                            <?php else: ?> <i class="fa fa-fw fa-times text-danger"></i>
                                            <?php endif; ?>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?> <i class="fa fa-fw fa-times text-danger"></i>

                                        <?php endif; ?>

                                    <?php elseif($kursus_row->tduga=='T'): ?>
                                        -
                                    <?php endif; ?>

                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </td>
                            <td style="vertical-align:middle; text-align:center;">
                <?php if(request()->input('SESIAKAD') == '2526'): ?>
                <?php
                                    if(request()->input('JENPROG')=='spm')
                                    {
                                        $kursus_sql  = \DB::connection('mysql_online')->select(DB::raw("SELECT tduga FROM 02_all_kodspm WHERE kod='".$fasa_mpb->{'PIL'.$p}."'"));
                                        // [origin] $peraku_tduga_sql  = \DB::connection('upu_cloud')->select(DB::raw("SELECT * FROM 2425_spm_peraku_tduga WHERE NOKP='".$fasa_mpb->NOKP."' AND PERAKU='".$fasa_mpb->{'PIL'.$p}."'"));
                                        $peraku_tduga_sql  = \DB::select(DB::raw("SELECT peraku AS PERAKU FROM emas.tbl_intviu_ranking WHERE nokp='".$fasa_mpb->NOKP."' AND peraku='".$fasa_mpb->{'PIL'.$p}."'"));
                                    }
                                    elseif(request()->input('JENPROG')=='stpm')
                                    {
                                        $kursus_sql  = \DB::connection('mysql_online')->select(DB::raw("SELECT tduga FROM 01_all_kodstpm WHERE kod='".$fasa_mpb->{'PIL'.$p}."'"));
                                        $peraku_tduga_sql  = \DB::connection('upu_cloud')->select(DB::raw("SELECT * FROM 2425_stpm_peraku_tduga WHERE NOKP='".$fasa_mpb->NOKP."' AND PERAKU='".$fasa_mpb->{'PIL'.$p}."'"));
                                    }
                                ?>

                                <?php $__currentLoopData = $kursus_sql; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $kursus_row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>

                                    <?php if($kursus_row->tduga=='Y'): ?>

                                        <?php $__empty_1 = true; $__currentLoopData = $peraku_tduga_sql; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $peraku_tduga_row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                            
                        <?php if(isset($peraku_tduga_row->PERAKU)): ?>
                        <i class="fa fa-fw fa-check text-success"></i>
                                            <?php else: ?>
                        <i class="fa fa-fw fa-times text-danger"></i>
                                            <?php endif; ?>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?> <i class="fa fa-fw fa-times text-danger"></i>

                                        <?php endif; ?>

                                    <?php elseif($kursus_row->tduga=='T'): ?>
                                        -
                                    <?php endif; ?>

                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php endif; ?>
                            </td>
                            <?php endif; ?>
                        </tr>
                    <?php endfor; ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tbody>
            </table>
        </div>

        <div class="row mt-n2 mb-3">
            <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12">
                <table class="tbl_font">
                    <tr>
                        <td>CARIAN OLEH : <?php echo e(strtoupper(Auth::user()->nama)); ?></td>
                    </tr>
                    <tr>
                        <td>JAWATAN : <?php echo e(strtoupper(Auth::user()->jawatan)); ?></td>
                    </tr>
                </table>
            </div>
        </div>

        <div class="row">
            <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12">
                <table class="tbl_font">
                    <tr>
                        <td class="td tbl_align"><strong>-</strong></td>
                        <td>Tidak Berkenaan</td>
                    </tr>
                    <tr>
                        <td class="td tbl_align"><strong>*</strong></td>
                        <td>Pemohon tidak memenuhi syarat minimum</td>
                    </tr>
                    <tr>
                        <td class="td tbl_align"><strong>?</strong></td>
                        <td>Program Pengajian dimohon tiada dalam senarai kursus pengajian</td>
                    </tr>
                    <tr>
                        <td class="td tbl_align"><strong>$</strong></td>
                        <td>Pemohon Sains mohon Program Pengajian Aliran Sastera</td>
                    </tr>
                    <tr>
                        <td class="td tbl_align"><strong>%</strong></td>
                        <td>Pemohon Sastera mohon Program Pengajian Aliran Sains</td>
                    </tr>
                </table>
            </div>

            <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12">
                <table class="tbl_font">
                    <tr>
                        <td class="td tbl_align"><span class="bg-success">&emsp; &emsp;</span></td>
                        <td> Program ditawarkan / Lulus Syarat</td>
                    </tr>
                    <tr>
                        <td class="td tbl_align"><span class="bg-danger">&emsp; &emsp;</span></td>
                        <td> Tidak lulus syarat</td>
                    </tr>
                </table>
            </div>
        </div>

    </div>
</div>


<div class="modal fade" id="syaratModal" tabindex="-1" role="dialog" aria-labelledby="syaratModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="syaratModalLabel">
                    <i class="fas fa-list-alt"></i> Syarat Program
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="syaratContent" style="max-height: 600px; overflow-y: auto;">
                <div class="text-center">
                    <i class="fas fa-spinner fa-spin text-info"></i>
                    <p class="mt-2">Memuat syarat program...</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fas fa-times"></i> Tutup
                </button>
                <button type="button" class="btn btn-primary" onclick="printSyarat()">
                    <i class="fas fa-print"></i> Cetak
                </button>
            </div>
        </div>
    </div>
</div>



<script>
    var textWatermark = '<?php echo e(Auth::user()->user_id); ?>';
    var body = document.getElementById('watermark_mohon');
    var background = "url(\"data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' version='1.1' height='150px' width='150px'>" +
        "<text transform='translate(20, 100) rotate(-30)' fill='rgba(128,128,128, 0.2)' font-size='20' >" + textWatermark + "</text></svg>\")";
    body.style.backgroundImage = background

    // Print syarat function
    function printSyarat() {
        let printContent = document.getElementById('syaratContent').innerHTML;
        let printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <!DOCTYPE html>
            <html>
            <head>
                <title>Syarat Program</title>
                <link href="<?php echo e(asset('vendor/bootstrap/css/bootstrap.min.css')); ?>" rel="stylesheet">
                <style>
                    @media  print {
                        body { font-size: 12px; }
                        .no-print { display: none; }
                        .table { page-break-inside: avoid; }
                    }
                </style>
            </head>
            <body>
                <div class="container-fluid">
                    ` + printContent + `
                </div>
            </body>
            </html>
        `);
        printWindow.document.close();
        printWindow.print();
    }




    // Show Syarat Test - opens specific program requirements in modal popup
    function showSyaratTest(kodprogram, kategori, jensetaraf = '') {
        // Show modal
        $('#syaratModal').modal('show');

        // Update modal title
        $('#syaratModalLabel').html('<i class="fas fa-list-alt"></i> Syarat Program: ' + kodprogram + ' (Kategori ' + kategori + ')');

        // Show loading content
        $('#syaratContent').html(`
            <div class="text-center">
                <i class="fas fa-spinner fa-spin text-info"></i>
                <p class="mt-2">Memuat syarat program untuk <strong>` + kodprogram + `</strong>...</p>
                <small class="text-muted">Kategori: ` + kategori + (jensetaraf ? `, Jensetaraf: ` + jensetaraf : '') + `</small>
            </div>
        `);

        // Construct URL for popup program view
        var url = '<?php echo e(url("stpm/esyaratkhas/popup")); ?>/' + kodprogram;
        if (kategori) {
            url += '/' + kategori;
        }
        // Add jensetaraf for E, F, G categories
        if (jensetaraf && ['E', 'F', 'G'].includes(kategori)) {
            url += '/' + jensetaraf;
        }

        // Load content via AJAX instead of opening new window
        $.ajax({
            url: url,
            method: 'GET',
            success: function(response) {
                $('#syaratContent').html(response);
            },
            error: function(xhr) {
                $('#syaratContent').html(`
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Maaf!</strong> Ralat memuat syarat program
                        <br><small>Kod Program: ` + kodprogram + `, Kategori: ` + kategori + `</small>
                    </div>
                `);
            }
        });
    }
</script>

<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\UPUCentre\resources\views/mc_profil_calon/index_mpb.blade.php ENDPATH**/ ?>