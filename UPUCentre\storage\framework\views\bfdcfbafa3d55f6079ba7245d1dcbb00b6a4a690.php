<?php if(!empty($syaratkhas_sk1)): ?>
<li style="padding-left: .3em;"> 
    Mendapat sekurang-kurangnya Gred <b><?php echo e($syaratkhas_sk1[0]->MINGRED_1); ?></b> dalam <b><?php echo e($syaratkhas_sk1[0]->KET_JUMLAH_MIN_SUBJEK_1); ?> (<?php echo e($syaratkhas_sk1[0]->JUMLAH_MIN_SUBJEK_1); ?>)</b> mata pelajaran <b>DAN</b> Gred <b><?php echo e($syaratkhas_sk1[0]->MINGRED_2); ?></b> dalam <b><?php echo e($syaratkhas_sk1[0]->KET_JUMLAH_MIN_SUBJEK_2); ?> (<?php echo e($syaratkhas_sk1[0]->JUMLAH_MIN_SUBJEK_2); ?>)</b> mata pelajaran berikut :
    <div class="card bg-light text-dark">
        <div class="card-body p-2">
            <?php $__currentLoopData = $syaratkhas_sk1; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $syarat_khas_sk1): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                

                <div class="col-md-12">
                    <span  style="display:table-cell;">&#9679;</span>
                    <span style="padding-left:5px; display:table-cell;"><?php echo e(ucwords(strtolower($syarat_khas_sk1->KODSUBJEK_2))); ?></span>
                </div>

            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
</li>
<?php endif; ?>



<?php /**PATH C:\xampp\htdocs\UPUCentre\resources\views/es_syaratkhas/spm/papar_syarat/syarat_khas_sk1.blade.php ENDPATH**/ ?>