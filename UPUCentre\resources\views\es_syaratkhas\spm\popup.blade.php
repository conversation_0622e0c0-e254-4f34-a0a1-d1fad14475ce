<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Syarat Program SPM - {{ $programCode ?? 'N/A' }}</title>
    <link href="{{ asset('vendor/bootstrap/css/bootstrap.min.css') }}" rel="stylesheet">
    <link href="{{ asset('vendor/fontawesome-free/css/all.min.css') }}" rel="stylesheet">

    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 0.9rem;
            background-color: #f8f9fa;
        }
        .popup-header {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        .program-info {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .syarat-content {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 20px;
        }
        ul { list-style-type: disc; }
        .badge { font-size: 0.85rem; }
        .text-muted { font-size: 0.8rem; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- Header -->
        <div class="popup-header">
            <h4 class="mb-1">
                <i class="fas fa-list-alt"></i>
                Syarat Program SPM
            </h4>
            <p class="mb-0">
                <strong>Kod Program:</strong> {{ $programCode ?? 'N/A' }}
                @if($kategori)
                    | <strong>Kategori:</strong> {{ $kategori }}
                @endif
            </p>
        </div>

        <!-- Debug Information -->
        <div class="alert alert-info">
            <h6>Debug Information:</h6>
            <p><strong>Program Code:</strong> {{ $programCode ?? 'NULL' }}</p>
            <p><strong>Kategori:</strong> {{ $kategori ?? 'NULL' }}</p>
            <p><strong>Program Count:</strong> {{ $program->count() ?? 'NULL' }}</p>
            <p><strong>Session Sesi:</strong> {{ session()->get('sesi_semasa') ?? 'NULL' }}</p>
            <p><strong>User IPTA:</strong> {{ Auth::user()->ipta ?? 'NULL' }}</p>
        </div>

        @if($program->count() > 0)
            @foreach($program as $senarai_program)
                <!-- Program Information -->
                <div class="program-info">
                    <div class="row">
                        <div class="col-md-8">
                            <h5 class="text-primary mb-2">
                                {{ $senarai_program->KODPROGRAM_PAPAR }} - {{ $senarai_program->NAMAPROGRAM }}
                            </h5>
                            <p class="mb-1">
                                <strong>Institusi:</strong> {{ $senarai_program->IPTA ?? 'N/A' }} |
                                <strong>Kategori:</strong> {{ $senarai_program->KATEGORI }} |
                                <strong>Sesi:</strong> {{ $senarai_program->SESI }}
                            </p>
                            @if($senarai_program->ALIRAN)
                                @if($senarai_program->ALIRAN=='1')
                                    <span class="badge bg-info text-white">Kumpulan 1</span>
                                @elseif($senarai_program->ALIRAN=='2')
                                    <span class="badge bg-info text-white">Kumpulan 2</span>
                                @elseif($senarai_program->ALIRAN=='3')
                                    <span class="badge bg-info text-white">Kumpulan 3</span>
                                @endif
                            @endif
                        </div>
                        <div class="col-md-4 text-right">
                            <small class="text-muted">
                                Status: {{ $senarai_program->STATUS_TAWAR == 'Y' ? 'Aktif' : 'Tidak Aktif' }}
                            </small>
                        </div>
                    </div>
                </div>

                <!-- Syarat Content -->
                <div class="syarat-content">
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-info-circle"></i> Temporary Debug Mode</h6>
                        <p>SPM Popup is working! Include files temporarily disabled for debugging.</p>
                        <p><strong>Program Found:</strong> {{ $senarai_program->NAMAPROGRAM }}</p>
                        <p><strong>IPTA:</strong> {{ $senarai_program->IPTA }}</p>
                        <p><strong>Kategori:</strong> {{ $senarai_program->KATEGORI }}</p>
                        <p><strong>Aliran:</strong> {{ $senarai_program->ALIRAN ?? 'N/A' }}</p>
                    </div>

                    {{-- Temporarily commented out for debugging
                    @php
                        $sessionSesi = session()->get('sesi_semasa');
                        include(app_path() . '/Http/Controllers/es_syarat/spm/inc_display_syarat/syaratkhas_nn.php');
                        include(app_path() . '/Http/Controllers/es_syarat/spm/inc_display_syarat/syaratkhas_g1.php');
                        include(app_path() . '/Http/Controllers/es_syarat/spm/inc_display_syarat/syaratkhas_f1.php');
                        include(app_path() . '/Http/Controllers/es_syarat/spm/inc_display_syarat/syaratkhas_f2.php');
                        include(app_path() . '/Http/Controllers/es_syarat/spm/inc_display_syarat/syaratkhas_f3.php');

                        include(app_path() . '/Http/Controllers/es_syarat/spm/inc_display_syarat/syaratkhas_g1.php');
                        include(app_path() . '/Http/Controllers/es_syarat/spm/inc_display_syarat/syaratkhas_g2.php');
                        include(app_path() . '/Http/Controllers/es_syarat/spm/inc_display_syarat/syaratkhas_g3.php');

                        include(app_path() . '/Http/Controllers/es_syarat/spm/inc_display_syarat/syaratkhas_ga.php');
                        include(app_path() . '/Http/Controllers/es_syarat/spm/inc_display_syarat/syaratkhas_gb.php');
                        include(app_path() . '/Http/Controllers/es_syarat/spm/inc_display_syarat/syaratkhas_sk1.php');
                        include(app_path() . '/Http/Controllers/es_syarat/spm/inc_display_syarat/syaratkhas_sk2.php');

                        include(app_path() . '/Http/Controllers/es_syarat/spm/inc_display_syarat/syarat_umur.php');
                        include(app_path() . '/Http/Controllers/es_syarat/spm/inc_display_syarat/syarat_kahwin.php');
                        include(app_path() . '/Http/Controllers/es_syarat/spm/inc_display_syarat/syarat_jantina.php');
                        include(app_path() . '/Http/Controllers/es_syarat/spm/inc_display_syarat/syarat_3M.php');
                    @endphp
                    --}}

                    <div class="clearfix">
                        <div class="pl-2 pb-3" style="line-height:35px; float:left; width: 100%; margin-top:-1px; margin-bottom: -35px;">
                            <h6 class="text-success mb-3">
                                <i class="fas fa-clipboard-list"></i> Syarat Khas Program SPM
                            </h6>

                            <div class="alert alert-success">
                                <h6><i class="fas fa-check-circle"></i> SPM Popup Working!</h6>
                                <p>The SPM popup is now functional. Syarat display logic will be implemented next.</p>
                                <p><strong>Next Step:</strong> Enable the include files and syarat display logic.</p>
                            </div>

                        </div>
                    </div>
                </div>
            @endforeach
        @else
            <!-- No Program Found -->
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>Tiada Data:</strong> Program dengan kod <strong>{{ $programCode }}</strong>
                @if($kategori) kategori <strong>{{ $kategori }}</strong> @endif tidak dijumpai.
            </div>
        @endif

        <!-- Footer -->
        <div class="text-center mt-4 mb-3">
            <small class="text-muted">
                <i class="fas fa-clock"></i>
                Dipaparkan pada: {{ date('d/m/Y H:i:s') }} |
                Sesi: {{ session()->get('sesi_semasa') }}
            </small>
        </div>
    </div>

    <!-- Scripts -->
    <script src="{{ asset('vendor/jquery/jquery.min.js') }}"></script>
    <script src="{{ asset('vendor/bootstrap/js/bootstrap.bundle.min.js') }}"></script>
</body>
</html>
