<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Syarat Program SPM - {{ $programCode ?? 'N/A' }}</title>
    <link href="{{ asset('vendor/bootstrap/css/bootstrap.min.css') }}" rel="stylesheet">
    <link href="{{ asset('vendor/fontawesome-free/css/all.min.css') }}" rel="stylesheet">
    
    @include('es_syaratkhas.spm.custom_css')
    
    <style>
        body { 
            font-family: Arial, sans-serif; 
            font-size: 0.9rem;
            background-color: #f8f9fa;
        }
        .popup-header {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        .program-info {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .syarat-content {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 20px;
        }
        ul { list-style-type: disc; }
        .badge { font-size: 0.85rem; }
        .text-muted { font-size: 0.8rem; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- Header -->
        <div class="popup-header">
            <h4 class="mb-1">
                <i class="fas fa-list-alt"></i> 
                Syarat Program SPM
            </h4>
            <p class="mb-0">
                <strong>Kod Program:</strong> {{ $programCode ?? 'N/A' }} 
                @if($kategori)
                    | <strong>Kategori:</strong> {{ $kategori }}
                @endif
            </p>
        </div>

        @if($program->count() > 0)
            @foreach($program as $senarai_program)
                <!-- Program Information -->
                <div class="program-info">
                    <div class="row">
                        <div class="col-md-8">
                            <h5 class="text-primary mb-2">
                                {{ $senarai_program->KODPROGRAM_PAPAR }} - {{ $senarai_program->NAMAPROGRAM }}
                            </h5>
                            <p class="mb-1">
                                <strong>Institusi:</strong> {{ $senarai_program->IPTA ?? 'N/A' }} |
                                <strong>Kategori:</strong> {{ $senarai_program->KATEGORI }} |
                                <strong>Sesi:</strong> {{ $senarai_program->SESI }}
                            </p>
                            @if($senarai_program->ALIRAN)
                                @if($senarai_program->ALIRAN=='1') 
                                    <span class="badge bg-info text-white">Kumpulan 1</span> 
                                @elseif($senarai_program->ALIRAN=='2') 
                                    <span class="badge bg-info text-white">Kumpulan 2</span> 
                                @elseif($senarai_program->ALIRAN=='3') 
                                    <span class="badge bg-info text-white">Kumpulan 3</span> 
                                @endif
                            @endif
                        </div>
                        <div class="col-md-4 text-right">
                            <small class="text-muted">
                                Status: {{ $senarai_program->STATUS_TAWAR == 'Y' ? 'Aktif' : 'Tidak Aktif' }}
                            </small>
                        </div>
                    </div>
                </div>

                <!-- Syarat Content -->
                <div class="syarat-content">
                    @php
                        $sessionSesi = session()->get('sesi_semasa');
                        include(app_path() . '/Http/Controllers/es_syarat/spm/inc_display_syarat/syaratkhas_nn.php');
                        include(app_path() . '/Http/Controllers/es_syarat/spm/inc_display_syarat/syaratkhas_g1.php');
                        include(app_path() . '/Http/Controllers/es_syarat/spm/inc_display_syarat/syaratkhas_f1.php');
                        include(app_path() . '/Http/Controllers/es_syarat/spm/inc_display_syarat/syaratkhas_f2.php');
                        include(app_path() . '/Http/Controllers/es_syarat/spm/inc_display_syarat/syaratkhas_f3.php');

                        include(app_path() . '/Http/Controllers/es_syarat/spm/inc_display_syarat/syaratkhas_g1.php');
                        include(app_path() . '/Http/Controllers/es_syarat/spm/inc_display_syarat/syaratkhas_g2.php');
                        include(app_path() . '/Http/Controllers/es_syarat/spm/inc_display_syarat/syaratkhas_g3.php');

                        include(app_path() . '/Http/Controllers/es_syarat/spm/inc_display_syarat/syaratkhas_ga.php');
                        include(app_path() . '/Http/Controllers/es_syarat/spm/inc_display_syarat/syaratkhas_gb.php');
                        include(app_path() . '/Http/Controllers/es_syarat/spm/inc_display_syarat/syaratkhas_sk1.php');
                        include(app_path() . '/Http/Controllers/es_syarat/spm/inc_display_syarat/syaratkhas_sk2.php');

                        include(app_path() . '/Http/Controllers/es_syarat/spm/inc_display_syarat/syarat_umur.php');
                        include(app_path() . '/Http/Controllers/es_syarat/spm/inc_display_syarat/syarat_kahwin.php');
                        include(app_path() . '/Http/Controllers/es_syarat/spm/inc_display_syarat/syarat_jantina.php');
                        include(app_path() . '/Http/Controllers/es_syarat/spm/inc_display_syarat/syarat_3M.php');
                    @endphp

                    <div class="clearfix">
                        <div class="pl-2 pb-3" style="line-height:35px; float:left; width: 100%; margin-top:-1px; margin-bottom: -35px;"> 
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-clipboard-list"></i> Syarat Khas Program
                            </h6>
                            
                            <ol style="padding-left: 1em;" style="list-style-type:decimal;">

                                @if(!empty($syaratkhas_nn))
                                    @include('es_syaratkhas.spm.papar_syarat.syarat_khas_nn')
                                @endif

                                @if(!empty($syaratkhas_g1) && empty($syaratkhas_ga))
                                    @include('es_syaratkhas.spm.papar_syarat.syarat_khas_g1')
                                @endif

                                @if(empty($syaratkhas_g1) && !empty($syaratkhas_ga))
                                    @include('es_syaratkhas.spm.papar_syarat.syarat_khas_ga')
                                @endif

                                @if(!empty($syaratkhas_g1) && !empty($syaratkhas_ga))
                                    @if(!empty($syaratkhas_sk1))
                                        @include('es_syaratkhas.spm.papar_syarat.syarat_khas_sk1')
                                    @endif
                                @endif

                                @if(!empty($syaratkhas_g2) && empty($syaratkhas_gb))
                                    @include('es_syaratkhas.spm.papar_syarat.syarat_khas_g2')
                                @endif

                                @if(empty($syaratkhas_g2) && !empty($syaratkhas_gb))
                                    @include('es_syaratkhas.spm.papar_syarat.syarat_khas_gb')
                                @endif

                                @if(!empty($syaratkhas_g2) && !empty($syaratkhas_gb))
                                    @if(!empty($syaratkhas_sk2))
                                        @include('es_syaratkhas.spm.papar_syarat.syarat_khas_sk2')
                                    @endif
                                @endif

                                @if(!empty($syaratkhas_g3))
                                    @include('es_syaratkhas.spm.papar_syarat.syarat_khas_g3')
                                @endif

                                @if(!empty($syaratkhas_f1))
                                    @include('es_syaratkhas.spm.papar_syarat.syarat_khas_f1')
                                @endif

                                @if(!empty($syaratkhas_f2))
                                    @include('es_syaratkhas.spm.papar_syarat.syarat_khas_f2')
                                @endif

                                @if(!empty($syaratkhas_f3))
                                    @include('es_syaratkhas.spm.papar_syarat.syarat_khas_f3')
                                @endif

                                {{-- Additional syarat sections --}}
                                @if(!empty($syarat_umur))
                                    @include('es_syaratkhas.spm.papar_syarat.syarat_umur')
                                @endif

                                @if(!empty($syarat_kahwin))
                                    @include('es_syaratkhas.spm.papar_syarat.syarat_kahwin')
                                @endif

                                @if(!empty($syarat_jantina))
                                    @include('es_syaratkhas.spm.papar_syarat.syarat_jantina')
                                @endif

                                @if(!empty($syarat_3M))
                                    @include('es_syaratkhas.spm.papar_syarat.syarat_3M')
                                @endif

                            </ol>

                            {{-- Show message if no syarat found --}}
                            @if(empty($syaratkhas_nn) && empty($syaratkhas_g1) && empty($syaratkhas_ga) && 
                                empty($syaratkhas_g2) && empty($syaratkhas_gb) && empty($syaratkhas_g3) && 
                                empty($syaratkhas_f1) && empty($syaratkhas_f2) && empty($syaratkhas_f3))
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i>
                                    <strong>Maklumat:</strong> Tiada syarat khas khusus untuk program ini. 
                                    Sila rujuk syarat am yang ditetapkan.
                                </div>
                            @endif

                        </div>
                    </div>
                </div>
            @endforeach
        @else
            <!-- No Program Found -->
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>Tiada Data:</strong> Program dengan kod <strong>{{ $programCode }}</strong> 
                @if($kategori) kategori <strong>{{ $kategori }}</strong> @endif tidak dijumpai.
            </div>
        @endif

        <!-- Footer -->
        <div class="text-center mt-4 mb-3">
            <small class="text-muted">
                <i class="fas fa-clock"></i> 
                Dipaparkan pada: {{ date('d/m/Y H:i:s') }} | 
                Sesi: {{ session()->get('sesi_semasa') }}
            </small>
        </div>
    </div>

    <!-- Scripts -->
    <script src="{{ asset('vendor/jquery/jquery.min.js') }}"></script>
    <script src="{{ asset('vendor/bootstrap/js/bootstrap.bundle.min.js') }}"></script>
</body>
</html>
