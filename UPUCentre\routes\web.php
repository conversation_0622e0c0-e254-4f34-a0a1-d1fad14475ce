<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ToggleModuleAccess;
/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/
















Auth::routes();

Route::get('/', function () {
	if(!Auth::check()) { return redirect('login'); } else { return redirect('/dashboard'); }
});

Route::group(['middleware' => ['prevent-back-history', 'fullaccess.user']],function(){
    Route::group(['prefix' => 'dashboard', 'middleware' => 'auth'], function () {
        Route::get('/','DashboardController@index');
        Route::get('/{nokp}/update', 'DashboardController@updateNotifikasi');
        route::post('/simpan', 'DashboardController@addInfo')->name('info.add');
        Route::get('/{id}/delete','DashboardController@destroyInfo');
    });

    Route::group(['prefix' => 'kalendar', 'middleware' => 'auth'], function () {
        Route::get('/', 'DashboardController@index')->name('events.index');
    });

    Route::group(['prefix' => 'aktiviti', 'middleware' => 'auth'], function () {
        Route::get('/','DashboardController@index');
        Route::post('/', 'DashboardController@addEvent')->name('events.add');
        Route::post('/update', 'DashboardController@updateEvent')->name('events.update');
        Route::get('/{id}/delete','DashboardController@destroy');
    });

    // ##########################################################################################################################################################################################
    // MAKLUMAT UMUM
    // ##########################################################################################################################################################################################

    // MODUL : INSTITUSI
    Route::group(['prefix' => 'institusi'], function () {
        Route::get('/','mu_InstitusiCTRL@index');
        Route::post('/', 'mu_InstitusiCTRL@instfilter' )->name('institusi.cari');
        Route::get('/simpan','mu_InstitusiCTRL@create');
        Route::post('/simpan','mu_InstitusiCTRL@store');
        Route::get('/{inst_id}','mu_InstitusiCTRL@show');
        Route::get('/{inst_id}/edit','mu_InstitusiCTRL@edit');
        Route::post('/{inst_id}/edit','mu_InstitusiCTRL@update');
        Route::get('/{inst_id}/delete','mu_InstitusiCTRL@destroy');
    });

    // MODUL : KAMPUS
    Route::group(['prefix' => 'kampus'], function () {
        Route::get('/','mu_KampusCTRL@index');
        Route::post('/', 'mu_KampusCTRL@kampusfilter')->name('kampus.cari');
        Route::post('/simpan','mu_KampusCTRL@store');
        Route::post('/tambah','mu_KampusCTRL@add_kampus');
    });

    // ##########################################################################################################################################################################################
    // PROGRAM PENGAJIAN
    // ##########################################################################################################################################################################################

    // MODUL : SENARAI PROGRAM
    Route::group(['prefix' => 'senarai'], function () {

        // UTAMA
        Route::get('/utama','pp_UtamaListCTRL@index');
        Route::post('/utama', 'pp_UtamaListCTRL@listfilter')->name('list.cari.utama');
        Route::post('/utama/simpan', 'pp_UtamaListCTRL@simpan_program')->name('senarai.utama.simpan');
        Route::get('/utama/{kodprogram}/batal','pp_UtamaListCTRL@batal_program');
		Route::get('/utama/cetak', 'pp_UtamaListCTRL@cetak_program')->name('cetak.utama.program');

        // RAYUAN
        Route::get('/rayuan','pp_RayuListCTRL@index');
        Route::post('/rayuan', 'pp_RayuListCTRL@listfilter')->name('list.cari.rayu');
        Route::post('/rayuan/simpan', 'pp_RayuListCTRL@simpan_program')->name('senarai.rayu.simpan');
        Route::get('/rayuan/{kodprogram}/batal','pp_RayuListCTRL@batal_program');
		Route::get('/rayuan/cetak', 'pp_RayuListCTRL@cetak_program')->name('cetak.rayu.program');
    });

    // MODUL : PERMOHONAN BARU
    Route::group(['prefix' => 'program'], function () {
        Route::get('/','pp_ProgramCTRL@index');
        Route::post('/', 'pp_ProgramCTRL@progfilter' )->name('program.cari');
        Route::get('/simpan','pp_ProgramCTRL@create');
        Route::post('/simpan','pp_ProgramCTRL@store');
        Route::get('/{kursus}','pp_ProgramCTRL@show');
        Route::get('/{kursus}/edit','pp_ProgramCTRL@edit');
        Route::post('/{kursus}/edit','pp_ProgramCTRL@update');
        Route::get('/{kursus}/delete','pp_ProgramCTRL@destroy');
        Route::get('/add/fakulti','pp_ProgramCTRL@fakulti');
        Route::post('/add/fakulti', 'pp_ProgramCTRL@fakultifilter' )->name('fakulti.cari');
        Route::post('/fakulti/simpan','pp_ProgramCTRL@fakulti_simpan');
        Route::get('/fakulti/{fakulti}/delete','pp_ProgramCTRL@fakulti_delete');
		route::get('/{kursus}/pdfview', 'pp_ProgramCTRL@pdfview')->name('pdfview');
    });




    // MODUL : YURAN
    Route::group(['prefix' => 'yuran'], function () {
        Route::get('/','pp_YuranCTRL@index'); // PAPARAN MODUL YURAN
        Route::post('/', 'pp_YuranCTRL@yuranfilter' )->name('yuran.cari'); // CARIAN MODUL YURAN
        Route::post('/update', 'pp_YuranCTRL@yuran_update'); // KEMASKINI MODUL YURAN PENGAJIAN

    });

    // MODUL : FAKULTI
    Route::group(['prefix' => 'fakulti'], function () {
        Route::get('/','pp_FakultiCTRL@index');
        Route::post('/', 'pp_FakultiCTRL@fakultifilter' )->name('fakulti.carian');
        Route::post('/simpan','pp_FakultiCTRL@fakulti_simpan');
        Route::post('/{id}/{kod}/update','pp_FakultiCTRL@fakulti_update');
        Route::get('/{kod}/{fakulti}/delete','pp_FakultiCTRL@program_delete');
        Route::get('/{kod}/{fakulti}/deletefakulti','pp_FakultiCTRL@fakulti_delete');
    });

    // ##########################################################################################################################################################################################
    // E-SYARAT (STPM)
    // ##########################################################################################################################################################################################

    // MODUL : PENAWARAN
    Route::group(['prefix' => 'stpm/penawaran'], function () {
        Route::get('/','es_syarat\stpm\es_Penawaran@index');
        Route::post('/', 'es_syarat\stpm\es_Penawaran@tawarfilter')->name('tawar.stpm.cari');
        Route::get('/{tawar}','es_syarat\stpm\es_Penawaran@show');
        Route::get('/2/simpan','es_syarat\stpm\es_Penawaran@create');
        Route::post('/2/simpan', 'es_syarat\stpm\es_Penawaran@store')->name('carian.stpm.daftar');
        Route::get('/{tawar}/edit','es_syarat\stpm\es_Penawaran@edit');
        Route::post('/{tawar}/edit','es_syarat\stpm\es_Penawaran@update');
        Route::get('/{tawar}/delete','es_syarat\stpm\es_Penawaran@destroy');

        Route::get('/2/cetak','es_syarat\stpm\es_Penawaran@cetak_tawar')->name('cetak.stpm.tawar');
    });

    // MODUL : MATAPELAJARAN
    Route::group(['prefix' => 'stpm/matapelajaran'], function () {
        Route::get('/','es_syarat\stpm\es_MataPelajaran@index');
        Route::post('/', 'es_syarat\stpm\es_MataPelajaran@subjekfilter' )->name('mp.stpm.cari');
        Route::get('/tambah','es_syarat\stpm\es_MataPelajaran@create');
        Route::post('/insert','es_syarat\stpm\es_MataPelajaran@insert');
        Route::get('/edit/{subjekkod}','es_syarat\stpm\es_MataPelajaran@edit');
        Route::post('/update','es_syarat\stpm\es_MataPelajaran@update');
        Route::get('/delete/{subjekkod}','es_syarat\stpm\es_MataPelajaran@delete');
        Route::get('/salin/{subjek}', 'es_syarat\stpm\es_MataPelajaran@modal_salin');
        Route::post('/salin', 'es_syarat\stpm\es_MataPelajaran@salin');
    });

    // MODUL : SYARAT AM
    Route::group(['prefix' => 'stpm/esyaratam'], function () {
        Route::get('/','es_syarat\stpm\es_SyaratAmCTRL@index');
        Route::get('/tambah','es_syarat\stpm\es_SyaratAmCTRL@create');
        Route::post('/insert','es_syarat\stpm\es_SyaratAmCTRL@insert');
        Route::get('/edit/{syaratam}','es_syarat\stpm\es_SyaratAmCTRL@edit');
        Route::post('/update','es_syarat\stpm\es_SyaratAmCTRL@update');
        Route::get('/delete/{syaratam}','es_syarat\stpm\es_SyaratAmCTRL@delete');
    });

    // MODUL : SYARAT KHAS
    Route::group(['prefix' => 'stpm/esyaratkhas'], function () {
        Route::get('/','es_syarat\stpm\es_SyaratKhasCTRL@index');
        Route::post('/', 'es_syarat\stpm\es_SyaratKhasCTRL@khasfilter' )->name('khas.stpm.cari');
        Route::get('/examples', function () { return view('es_syaratkhas.stpm.examples'); })->name('khas.stpm.examples');
        Route::get('/specific/{programCode}/{kategori?}','es_syarat\stpm\es_SyaratKhasCTRL@showSpecificProgram')->name('khas.stpm.specific');
        Route::get('/popup/{programCode}/{kategori?}','es_syarat\stpm\es_SyaratKhasCTRL@showSpecificProgramPopup')->name('khas.stpm.popup');
        Route::get('/edit/{sk_kodprogram}','es_syarat\stpm\es_SyaratKhasCTRL@edit');
        Route::get('/editdip/{sk_kodprogram}/{sk_jensetaraf}','es_syarat\stpm\es_SyaratKhasCTRL@editdip');
        Route::post('/update', 'es_syarat\stpm\es_SyaratKhasCTRL@update');
        Route::post('/update2', 'es_syarat\stpm\es_SyaratKhasCTRL@update_dip');
        Route::get('/salin/{idsyarat}', 'es_syarat\stpm\es_SyaratKhasCTRL@modal_salin');
        Route::get('/salin2/{idsyarat}/{idjensetaraf}', 'es_syarat\stpm\es_SyaratKhasCTRL@modal_salin_diploma');
        Route::post('/salin', 'es_syarat\stpm\es_SyaratKhasCTRL@salin');
        Route::post('/salin2', 'es_syarat\stpm\es_SyaratKhasCTRL@salin2');
        Route::get('/preview','es_syarat\stpm\es_SyaratKhasCTRL@prnpreview'); // CETAK PAPARAN SYARAT KHAS
        Route::get('/cetakoku','es_syarat\stpm\es_SyaratKhasCTRL@cetak_oku')->name('cetakoku.stpm.oku'); // CETAK SENARAI OKU
    });

    // MODUL : BIDANG NEC
    Route::group(['prefix' => 'stpm/bidangnec'], function () {
        Route::get('/','es_BidangCTRL@index');
        Route::post('/', 'es_BidangCTRL@necfilter' )->name('bidangnec.cari');
        Route::post('/insert','es_BidangCTRL@nec_insert');
        Route::get('/{id}/{kod}/{nec}/delete','es_BidangCTRL@destroy');
        Route::get('/cetak','es_BidangCTRL@cetak_nec')->name('cetak.stpm.nec'); // CETAK SENARAI KOD NEC
    });

    // MODUL : TAFSIRAN SYARAT
    Route::group(['prefix' => 'tafsiran'], function () {
        Route::get('/stpm','es_tafsiranCTRL@index');
        Route::post('/stpm/', 'es_tafsiranCTRL@tafsirfilter' )->name('tafsir.stpm.cari');
        Route::get('/stpm/generate','es_tafsiranCTRL@generate_tafsiran_stpm');
        Route::get('/stpm/preview','es_tafsiranCTRL@prnpreview');
    });

    // MODUL : PERBANDINGAN SYARAT
    Route::group(['prefix' => 'perbandingan'], function () {
        Route::get('/stpm','es_perbandinganCTRL@index');
        Route::post('/stpm', 'es_perbandinganCTRL@bandingfilter' )->name('banding.cari');
        Route::get('/stpm/generate','es_perbandinganCTRL@generate_banding_stpm');
        Route::get('/stpm/preview','es_perbandinganCTRL@prnpreview');
    });

    // ##########################################################################################################################################################################################
    // E-SYARAT (SPM)
    // ##########################################################################################################################################################################################

    // MODUL : PENAWARAN
    Route::group(['prefix' => 'penawaran'], function () {
        Route::get('/','es_syarat\spm\es_Penawaran@index');
        Route::post('/', 'es_syarat\spm\es_Penawaran@tawarfilter')->name('tawar.spm.cari');
        Route::get('/{tawar}','es_syarat\spm\es_Penawaran@show');
        Route::get('/2/simpan','es_syarat\spm\es_Penawaran@create');
        Route::post('/2/simpan', 'es_syarat\spm\es_Penawaran@store')->name('carian.spm.daftar');
        Route::get('/{tawar}/edit','es_syarat\spm\es_Penawaran@edit');
        Route::post('/{tawar}/edit','es_syarat\spm\es_Penawaran@update');
        Route::get('/{tawar}/delete','es_syarat\spm\es_Penawaran@destroy');

        Route::get('/2/cetak','es_syarat\spm\es_Penawaran@cetak_tawar')->name('cetak.spm.tawar');
    });

    // MODUL : MATAPELAJARAN
    Route::group(['prefix' => 'matapelajaran'], function () {
        Route::get('/','es_syarat\spm\es_MataPelajaran@index');
        Route::post('/', 'es_syarat\spm\es_MataPelajaran@subjekfilter' )->name('mp.spm.cari');
        Route::get('/tambah','es_syarat\spm\es_MataPelajaran@create');
        Route::post('/insert','es_syarat\spm\es_MataPelajaran@insert');
        Route::get('/edit/{subjekkod}','es_syarat\spm\es_MataPelajaran@edit');
        Route::post('/update','es_syarat\spm\es_MataPelajaran@update');
        Route::get('/delete/{subjekkod}','es_syarat\spm\es_MataPelajaran@delete');
        Route::get('/salin/{subjek}', 'es_syarat\spm\es_MataPelajaran@modal_salin');
        Route::post('/salin', 'es_syarat\spm\es_MataPelajaran@salin');
    });

    // MODUL : SYARAT AM
    Route::group(['prefix' => 'esyaratam'], function () {
        Route::get('/','es_syarat\spm\es_SyaratAmCTRL@index');
        Route::get('/tambah','es_syarat\spm\es_SyaratAmCTRL@create');
        Route::post('/insert','es_syarat\spm\es_SyaratAmCTRL@insert');
        Route::get('/edit/{syaratam}','es_syarat\spm\es_SyaratAmCTRL@edit');
        Route::post('/update','es_syarat\spm\es_SyaratAmCTRL@update');
        Route::get('/delete/{syaratam}','es_syarat\spm\es_SyaratAmCTRL@delete');
    });

    // MODUL : SYARAT KHAS
    Route::group(['prefix' => 'esyaratkhas'], function () {
        Route::get('/','es_syarat\spm\es_SyaratKhasCTRL@index');
        Route::post('/', 'es_syarat\spm\es_SyaratKhasCTRL@khasfilter' )->name('khas.spm.cari');
        Route::get('/popup/{programCode}/{kategori?}','es_syarat\spm\es_SyaratKhasCTRL@showSpecificProgramPopup')->name('khas.spm.popup');
        Route::get('/edit/{sk_kodprogram}','es_syarat\spm\es_SyaratKhasCTRL@edit');
        Route::post('/update', 'es_syarat\spm\es_SyaratKhasCTRL@update');
        Route::post('/salin', 'es_syarat\spm\es_SyaratKhasCTRL@salin');
        Route::get('/salin/{idsyarat}', 'es_syarat\spm\es_SyaratKhasCTRL@modal_salin');
        Route::get('/preview','es_syarat\spm\es_SyaratKhasCTRL@prnpreview'); // CETAK PAPARAN SYARAT KHAS
		Route::get('/cetakoku','es_syarat\spm\es_SyaratKhasCTRL@cetak_oku')->name('cetakoku.spm.oku'); // CETAK SENARAI OKU
    });

    Route::group(['prefix' => 'kelayakan'], function () {
        Route::get('/','KelayakanCTRL@index');
        Route::post('/semak', 'KelayakanCTRL@semak');
    });

    // MODUL : TAFSIRAN SYARAT
    Route::group(['prefix' => 'tafsiran'], function () {
            Route::get('/','es_tafsiranCTRL@index');
            Route::post('/', 'es_tafsiranCTRL@tafsirfilter' )->name('tafsir.cari');
            Route::get('/preview','es_tafsiranCTRL@prnpreview');
    });

    // MODUL : PERBANDINGAN SYARAT
    Route::group(['prefix' => 'perbandingan'], function () {
        Route::get('/','es_perbandinganCTRL@index');
        Route::post('/', 'es_perbandinganCTRL@bandingfilter' )->name('banding.cari');
        Route::get('/generate','es_perbandinganCTRL@generate_banding_spm');
        Route::get('/preview','es_perbandinganCTRL@prnpreview');
    });

    // ##########################################################################################################################################################################################
    // UNJURAN
    // ##########################################################################################################################################################################################

    Route::group(['prefix' => 'unjuran2'], function () {

        // ************************************************************
        // UNJURAN RASMI
        // ************************************************************

        // MODUL : UNJURAN RASMI (SPM UA)
        Route::get('/ua','unj_rasmi_spm_UnjuranCTRL@index');
        Route::post('/ua', 'unj_rasmi_spm_UnjuranCTRL@khasfilter' )->name('unjuran2.ua.cari');
        Route::get('/ua/{ipta}/delete','unj_rasmi_spm_UnjuranCTRL@destroy');
        Route::get('/ua/deleteall','unj_rasmi_spm_UnjuranCTRL@destroyall');
        Route::post('/ua/simpan', 'unj_rasmi_spm_UnjuranCTRL@simpan_program')->name('unjuran2.ua.simpan');
        Route::get('/ua/{kod}/sah_unjuran', 'unj_rasmi_spm_UnjuranCTRL@pengesahan_unjuran');
        Route::get('/ua/sah_pukal', 'unj_rasmi_spm_UnjuranCTRL@pengesahan_pukal_unjuran');
        Route::get('/spm/preview','unj_rasmi_spm_UnjuranCTRL@prnpreview'); // CETAK SENARAI TAWAR
        Route::get('/spm/export','unj_rasmi_spm_UnjuranCTRL@export')->name('unjuran2.spm.export');
        Route::get('/spm/borang', 'unj_rasmi_spm_UnjuranCTRL@borang_unjuran')->name('cetak.spm.borang');
		Route::get('/spm/reset', 'unj_rasmi_spm_UnjuranCTRL@reset_unjuran');

        // MODUL : UNJURAN RASMI (SPM ILKA)
        Route::get('/ilka','unj_rasmi_ilka_UnjuranCTRL@index');
        Route::post('/ilka', 'unj_rasmi_ilka_UnjuranCTRL@khasfilter' )->name('unjuran2.ilka.cari');
        Route::get('/ilka/{ilka}/{pusat}/delete','unj_rasmi_ilka_UnjuranCTRL@destroy');
        Route::get('/ilka/deleteall','unj_rasmi_ilka_UnjuranCTRL@destroyall');
        Route::post('/ilka/simpan', 'unj_rasmi_ilka_UnjuranCTRL@simpan_program')->name('unjuran2.ilka.simpan');
        Route::get('/ilka/{kod}/{pusat}/sah_unjuran', 'unj_rasmi_ilka_UnjuranCTRL@pengesahan_unjuran');
        Route::get('/ilka/sah_pukal', 'unj_rasmi_ilka_UnjuranCTRL@pengesahan_pukal_unjuran');
        Route::get('/ilka/preview','unj_rasmi_ilka_UnjuranCTRL@prnpreview'); // CETAK SENARAI TAWAR
        Route::get('/ilka/export','unj_rasmi_ilka_UnjuranCTRL@export')->name('unjuran2.ilka.export');
        Route::get('/ilka/borang', 'unj_rasmi_ilka_UnjuranCTRL@borang_unjuran')->name('cetak.ilka.borang');
        Route::get('/ilka/reset', 'unj_rasmi_ilka_UnjuranCTRL@reset_unjuran');

        // MODUL : UNJURAN RASMI (SPM ILKA JPPKK)
        Route::get('/ilka/jppkk','unj_rasmi_ilka_jppkk_UnjuranCTRL@index');
        Route::post('/ilka/jppkk', 'unj_rasmi_ilka_jppkk_UnjuranCTRL@khasfilter' )->name('unjuran2.ilka.jppkk.cari');
        Route::get('/ilka/jppkk/{ilka}/{pusat}/delete','unj_rasmi_ilka_jppkk_UnjuranCTRL@destroy');
        Route::get('/ilka/jppkk/deleteall','unj_rasmi_ilka_jppkk_UnjuranCTRL@destroyall');
        Route::post('/ilka/jppkk/simpan', 'unj_rasmi_ilka_jppkk_UnjuranCTRL@simpan_program')->name('unjuran2.ilka.jppkk.simpan');
        Route::get('/ilka/jppkk/{kod}/{pusat}/sah_unjuran', 'unj_rasmi_ilka_jppkk_UnjuranCTRL@pengesahan_unjuran');
        Route::get('/ilka/jppkk/sah_pukal', 'unj_rasmi_ilka_jppkk_UnjuranCTRL@pengesahan_pukal_unjuran');
        Route::get('/ilka/jppkk/preview','unj_rasmi_ilka_jppkk_UnjuranCTRL@prnpreview'); // CETAK SENARAI TAWAR
        Route::get('/ilka/jppkk/export','unj_rasmi_ilka_jppkk_UnjuranCTRL@export')->name('unjuran2.ilka.jppkk.export');
        Route::get('/ilka/jppkk/borang', 'unj_rasmi_ilka_jppkk_UnjuranCTRL@borang_unjuran')->name('cetak.ilka.jppkk.borang');
        Route::get('/ilka/jppkk/reset', 'unj_rasmi_ilka_jppkk_UnjuranCTRL@reset_unjuran');

        // MODUL : UNJURAN RASMI (STPM)
        Route::get('/stpm','unj_rasmi_stpm_UnjuranCTRL@index');
        Route::post('/stpm', 'unj_rasmi_stpm_UnjuranCTRL@khasfilter' )->name('unjuran2.stpm.cari');
        Route::get('/stpm/{stpm}/delete','unj_rasmi_stpm_UnjuranCTRL@destroy');
        Route::get('/stpm/deleteall','unj_rasmi_stpm_UnjuranCTRL@destroyall');
        Route::post('/stpm/simpan', 'unj_rasmi_stpm_UnjuranCTRL@simpan_program')->name('unjuran2.stpm.simpan');
        Route::get('/stpm/{kod}/sah_unjuran', 'unj_rasmi_stpm_UnjuranCTRL@pengesahan_unjuran');
        Route::get('/stpm/sah_pukal', 'unj_rasmi_stpm_UnjuranCTRL@pengesahan_pukal_unjuran');
        Route::get('/stpm/preview','unj_rasmi_stpm_UnjuranCTRL@prnpreview'); // CETAK SENARAI TAWAR
        Route::get('/stpm/export','unj_rasmi_stpm_UnjuranCTRL@export')->name('unjuran2.stpm.export');
        Route::get('/stpm/borang', 'unj_rasmi_stpm_UnjuranCTRL@borang_unjuran')->name('cetak.stpm.borang');
        Route::get('/stpm/reset', 'unj_rasmi_stpm_UnjuranCTRL@reset_unjuran');

        // ************************************************************
        // UNJURAN JACKUP
        // ************************************************************

        // MODUL : UNJURAN JACKUP (SPM UA)
        Route::get('/jackup/ua','unj_jackup_spm_UnjuranCTRL@index');
        Route::post('/jackup/ua', 'unj_jackup_spm_UnjuranCTRL@khasfilter' )->name('unjuran2.jackup.ua.cari');
        Route::get('/jackup/ua/{ipta}/delete','unj_jackup_spm_UnjuranCTRL@destroy');
        Route::get('/jackup/ua/deleteall','unj_jackup_spm_UnjuranCTRL@destroyall');
        Route::post('/jackup/ua/simpan', 'unj_jackup_spm_UnjuranCTRL@simpan_program')->name('unjuran2.jackup.ua.simpan');
        Route::get('/jackup/ua/{kod}/sah_unjuran', 'unj_jackup_spm_UnjuranCTRL@pengesahan_unjuran');
        Route::get('/jackup/spm/preview','unj_jackup_spm_UnjuranCTRL@prnpreview');
        Route::get('/jackup/spm/export','unj_jackup_spm_UnjuranCTRL@export')->name('unjuran2.jackup.spm.export');
        Route::get('/jackup/spm/borang', 'unj_jackup_spm_UnjuranCTRL@borang_unjuran')->name('cetak.jackup.spm.borang');
		Route::get('/jackup/spm/reset', 'unj_jackup_spm_UnjuranCTRL@reset_unjuran');

        // MODUL : UNJURAN JACKUP (SPM ILKA)
        Route::get('/jackup/ilka','unj_jackup_ilka_UnjuranCTRL@index');
        Route::post('/jackup/ilka', 'unj_jackup_ilka_UnjuranCTRL@khasfilter' )->name('unjuran2.jackup.ilka.cari');
        Route::get('/jackup/ilka/{ilka}/{pusat}/delete','unj_jackup_ilka_UnjuranCTRL@destroy');
        Route::get('/jackup/ilka/deleteall','unj_jackup_ilka_UnjuranCTRL@destroyall');
        Route::post('/jackup/ilka/simpan', 'unj_jackup_ilka_UnjuranCTRL@simpan_program')->name('unjuran2.jackup.ilka.simpan');
        Route::get('/jackup/ilka/{kod}/{pusat}/sah_unjuran', 'unj_jackup_ilka_UnjuranCTRL@pengesahan_unjuran');
        Route::get('/jackup/ilka/preview','unj_jackup_ilka_UnjuranCTRL@prnpreview');
        Route::get('/jackup/ilka/export','unj_jackup_ilka_UnjuranCTRL@export')->name('unjuran2.jackup.ilka.export');
        Route::get('/jackup/ilka/borang', 'unj_jackup_ilka_UnjuranCTRL@borang_unjuran')->name('cetak.jackup.ilka.borang');
        Route::get('/jackup/ilka/reset', 'unj_jackup_ilka_UnjuranCTRL@reset_unjuran');

        // MODUL : UNJURAN JACKUP (SPM ILKA JPPKK)
        Route::get('/jackup/ilka/jppkk','unj_jackup_ilka_jppkk_UnjuranCTRL@index');
        Route::post('/jackup/ilka/jppkk', 'unj_jackup_ilka_jppkk_UnjuranCTRL@khasfilter' )->name('unjuran2.jackup.ilka.jppkk.cari');
        Route::get('/jackup/ilka/jppkk/{ilka}/{pusat}/delete','unj_jackup_ilka_jppkk_UnjuranCTRL@destroy');
        Route::get('/jackup/ilka/jppkk/deleteall','unj_jackup_ilka_jppkk_UnjuranCTRL@destroyall');
        Route::post('/jackup/ilka/jppkk/simpan', 'unj_jackup_ilka_jppkk_UnjuranCTRL@simpan_program')->name('unjuran2.jackup.ilka.jppkk.simpan');
        Route::get('/jackup/ilka/jppkk/{kod}/{pusat}/sah_unjuran', 'unj_jackup_ilka_jppkk_UnjuranCTRL@pengesahan_unjuran');
        Route::get('/jackup/ilka/jppkk/sah_pukal', 'unj_jackup_ilka_jppkk_UnjuranCTRL@pengesahan_pukal_unjuran');
        Route::get('/jackup/ilka/jppkk/preview','unj_jackup_ilka_jppkk_UnjuranCTRL@prnpreview'); // CETAK SENARAI TAWAR
        Route::get('/jackup/ilka/jppkk/export','unj_jackup_ilka_jppkk_UnjuranCTRL@export')->name('unjuran2.jackup.ilka.jppkk.export');
        Route::get('/jackup/ilka/jppkk/borang', 'unj_jackup_ilka_jppkk_UnjuranCTRL@borang_unjuran')->name('cetak.jackup.ilka.jppkk.borang');
        Route::get('/jackup/ilka/jppkk/reset', 'unj_jackup_ilka_jppkk_UnjuranCTRL@reset_unjuran');

        // MODUL : UNJURAN JACKUP (STPM)
        Route::get('/jackup/stpm','unj_jackup_stpm_unjuranCTRL@index');
        Route::post('/jackup/stpm', 'unj_jackup_stpm_unjuranCTRL@khasfilter' )->name('unjuran2.jackup.stpm.cari');
        Route::get('/jackup/stpm/{stpm}/delete','unj_jackup_stpm_unjuranCTRL@destroy');
        Route::get('/jackup/stpm/deleteall','unj_jackup_stpm_unjuranCTRL@destroyall');
        Route::post('/jackup/stpm/simpan', 'unj_jackup_stpm_unjuranCTRL@simpan_program')->name('unjuran2.jackup.stpm.simpan');
        Route::get('/jackup/stpm/{kod}/sah_unjuran', 'unj_jackup_stpm_unjuranCTRL@pengesahan_unjuran');
        Route::get('/jackup/stpm/preview','unj_jackup_stpm_unjuranCTRL@prnpreview');
        Route::get('/jackup/stpm/export','unj_jackup_stpm_unjuranCTRL@export')->name('unjuran2.jackup.stpm.export');
        Route::get('/jackup/stpm/borang', 'unj_jackup_stpm_unjuranCTRL@borang_unjuran')->name('cetak.jackup.stpm.borang');
		Route::get('/jackup/stpm/reset', 'unj_jackup_stpm_unjuranCTRL@reset_unjuran');

        // ************************************************************
        // UNJURAN RAYUAN
        // ************************************************************

        // MODUL : UNJURAN RAYUAN (SPM UA)
        Route::get('/rayuan/ua','unj_rayu_spm_UnjuranCTRL@index');
        Route::post('/rayuan/ua', 'unj_rayu_spm_UnjuranCTRL@khasfilter' )->name('unjuran2.rayuan.ua.cari');
        Route::get('/rayuan/ua/{ipta}/delete','unj_rayu_spm_UnjuranCTRL@destroy');
        Route::get('/rayuan/ua/deleteall','unj_rayu_spm_UnjuranCTRL@destroyall');
        Route::post('/rayuan/ua/simpan', 'unj_rayu_spm_UnjuranCTRL@simpan_program')->name('unjuran2.rayuan.ua.simpan');
        Route::get('/rayuan/ua/{kod}/sah_unjuran', 'unj_rayu_spm_UnjuranCTRL@pengesahan_unjuran');
        Route::get('/rayuan/ua/sah_pukal', 'unj_rayu_spm_UnjuranCTRL@pengesahan_pukal_unjuran');
        Route::get('/rayuan/spm/preview','unj_rayu_spm_UnjuranCTRL@prnpreview'); // CETAK SENARAI TAWAR
        Route::get('/rayuan/spm/export','unj_rayu_spm_UnjuranCTRL@export')->name('unjuran2.rayuan.spm.export');
        Route::get('/rayuan/spm/borang', 'unj_rayu_spm_UnjuranCTRL@borang_unjuran')->name('cetak.rayuan.spm.borang');

        // MODUL : UNJURAN RAYUAN (SPM ILKA) - DISABLED
	Route::get('/rayuan/ilka', function(){
		return view('lock.tutup');
	});

        // MODUL : UNJURAN RAYUAN (SPM ILKA JPPKK)
        Route::get('/rayuan/ilka/jppkk','unj_rayu_ilka_jppkk_UnjuranCTRL@index');
        Route::post('/rayuan/ilka/jppkk', 'unj_rayu_ilka_jppkk_UnjuranCTRL@khasfilter' )->name('unjuran2.rayuan.ilka.jppkk.cari');
        Route::get('/rayuan/ilka/jppkk/{ilka}/{pusat}/delete','unj_rayu_ilka_jppkk_UnjuranCTRL@destroy');
        Route::get('/rayuan/ilka/jppkk/deleteall','unj_rayu_ilka_jppkk_UnjuranCTRL@destroyall');
        Route::post('/rayuan/ilka/jppkk/simpan', 'unj_rayu_ilka_jppkk_UnjuranCTRL@simpan_program')->name('unjuran2.rayuan.ilka.jppkk.simpan');
        Route::get('/rayuan/ilka/jppkk/{kod}/{pusat}/sah_unjuran', 'unj_rayu_ilka_jppkk_UnjuranCTRL@pengesahan_unjuran');
        Route::get('/rayuan/ilka/jppkk/sah_pukal', 'unj_rayu_ilka_jppkk_UnjuranCTRL@pengesahan_pukal_unjuran');
        Route::get('/rayuan/ilka/jppkk/preview','unj_rayu_ilka_jppkk_UnjuranCTRL@prnpreview'); // CETAK SENARAI TAWAR
        Route::get('/rayuan/ilka/jppkk/export','unj_rayu_ilka_jppkk_UnjuranCTRL@export')->name('unjuran2.rayuan.ilka.jppkk.export');
        Route::get('/rayuan/ilka/jppkk/borang', 'unj_rayu_ilka_jppkk_UnjuranCTRL@borang_unjuran')->name('cetak.rayuan.ilka.jppkk.borang');

        // MODUL : UNJURAN RAYUAN (STPM)
        Route::get('/rayuan/stpm','unj_rayu_stpm_UnjuranCTRL@index');
        Route::post('/rayuan/stpm', 'unj_rayu_stpm_UnjuranCTRL@khasfilter' )->name('unjuran2.rayuan.stpm.cari');
        Route::get('/rayuan/stpm/{stpm}/delete','unj_rayu_stpm_UnjuranCTRL@destroy');
        Route::get('/rayuan/stpm/deleteall','unj_rayu_stpm_UnjuranCTRL@destroyall');
        Route::post('/rayuan/stpm/simpan', 'unj_rayu_stpm_UnjuranCTRL@simpan_program')->name('unjuran2.rayuan.stpm.simpan');
        Route::get('/rayuan/stpm/{kod}/sah_unjuran', 'unj_rayu_stpm_UnjuranCTRL@pengesahan_unjuran');
        Route::get('/rayuan/stpm/sah_pukal', 'unj_rayu_stpm_UnjuranCTRL@pengesahan_pukal_unjuran');
        Route::get('/rayuan/stpm/preview','unj_rayu_stpm_UnjuranCTRL@prnpreview'); // CETAK SENARAI TAWAR
        Route::get('/rayuan/stpm/export','unj_rayu_stpm_UnjuranCTRL@export')->name('unjuran2.rayuan.stpm.export');
        Route::get('/rayuan/stpm/borang', 'unj_rayu_stpm_UnjuranCTRL@borang_unjuran')->name('cetak.rayuan.stpm.borang');
    });

    // ##########################################################################################################################################################################################
    // LALUAN KERJAYA
    // ##########################################################################################################################################################################################

    // MODUL : LALUAN KERJAYA
    Route::group(['prefix' => 'kerjaya'], function () {
        Route::get('/','lk_KerjayaCTRL@index');
        Route::post('/', 'lk_KerjayaCTRL@kerjayafilter')->name('kerjaya.cari');
        Route::post('/simpan','lk_KerjayaCTRL@store')->name('kerjaya.store');
        Route::post('/kemaskini','lk_KerjayaCTRL@update')->name('kerjaya.update');
		Route::get('/export','lk_KerjayaCTRL@export')->name('kerjaya.export');
    });

    // ##########################################################################################################################################################################################
    // MAKLUMAT CALON
    // ##########################################################################################################################################################################################

    // MODUL : FIN CALON
    Route::group(['prefix' => 'fin'], function () {
        Route::get('/','mc_FinCalonCTRL@index');
        Route::post('/', 'mc_FinCalonCTRL@finfilter')->name('fin.cari');
    });

    // MODUL : CARIAN CALON
    Route::group(['prefix' => 'carian_pemohon'], function () {
        Route::get('/','mc_CarianCalonCTRL@index');
        Route::post('/', 'mc_CarianCalonCTRL@calonfilter')->name('calon.cari');
    });

    // MODUL : REKOD MOHON
    Route::group(['prefix' => 'upuadmin'], function () {
        Route::get('/','mc_RekodMohonCTRL@index');
        Route::post('/', 'mc_RekodMohonCTRL@carian' )->name('upuadmin.cari');



        // New consolidated Semakan Pelajar routes for tabbed UI in /upuadmin
        // These keep all actions within the same page and use mc_RekodMohonCTRL methods
        Route::post('/semakan-single', 'mc_RekodMohonCTRL@checkSingle')->name('upuadmin.semakan-single');
        Route::post('/semakan-upload', 'mc_RekodMohonCTRL@uploadExcelBatch')->name('upuadmin.semakan-upload');
        Route::get('/semakan-export-excel', 'mc_RekodMohonCTRL@exportExcel')->name('upuadmin.semakan-export-excel');
        Route::get('/semakan-export-pdf', 'mc_RekodMohonCTRL@exportPdf')->name('upuadmin.semakan-export-pdf');
        Route::post('/semakan-clear-single', 'mc_RekodMohonCTRL@clearSingle')->name('upuadmin.semakan-clear-single');
        Route::post('/semakan-clear-excel', 'mc_RekodMohonCTRL@clearExcel')->name('upuadmin.semakan-clear-excel');
    });

    // MODUL : PROFIL CALON (BKPA & UA/ILKA)
    Route::group(['prefix' => 'profilcalon'], function () {
        Route::get('/','mc_ProfilCalonCTRL@index');
        Route::post('/', 'mc_ProfilCalonCTRL@carian' )->name('pcarian');
		Route::get('/BORANG_PENGESAHAN_CALON_SULUNG', 'mc_ProfilCalonCTRL@borang_sulung')->name('cetak.sulung');
    });



        // SUB MODUL DARI REKOD MOHON & PROFIL CALON : VIEW DIPLOMA DOKUMEN
        Route::group(['prefix' => 'dokumen'], function () {
            route::get('/spm', 'mc_RekodMohonCTRL@spm')->name('spm');
			route::get('/transkrip_svm', 'mc_RekodMohonCTRL@tsvm')->name('tsvm');
            route::get('/svm', 'mc_RekodMohonCTRL@svm')->name('svm');
            route::get('/muet', 'mc_RekodMohonCTRL@muet')->name('muet');
            route::get('/alevel', 'mc_RekodMohonCTRL@alevel')->name('alevel');
            route::get('/ib', 'mc_RekodMohonCTRL@ib')->name('ib');
            route::get('/stpm', 'mc_RekodMohonCTRL@stpm')->name('stpm');
            route::get('/matrik', 'mc_RekodMohonCTRL@matrik')->name('matrik');
            route::get('/stam', 'mc_RekodMohonCTRL@stam')->name('stam');
            route::get('/ausmat', 'mc_RekodMohonCTRL@ausmat')->name('ausmat');
            route::get('/transkrip', 'mc_RekodMohonCTRL@transkrip')->name('transkrip');
            route::get('/diploma', 'mc_RekodMohonCTRL@diploma')->name('diploma');
            route::get('/bmjulai', 'mc_RekodMohonCTRL@bmjulai')->name('bmjulai');
            route::get('/matejulai', 'mc_RekodMohonCTRL@matejulai')->name('matejulai');
            route::get('/sejjulai', 'mc_RekodMohonCTRL@sejjulai')->name('sejjulai');
            route::get('/akreditasi', 'mc_RekodMohonCTRL@akreditasi')->name('akreditasi');
            route::get('/majikan', 'mc_RekodMohonCTRL@majikan')->name('majikan');

            route::get('/spm1', 'mc_ProfilCalonCTRL@spm')->name('spm1');
			route::get('/transkrip_svm1', 'mc_ProfilCalonCTRL@tsvm')->name('tsvm1');
            route::get('/svm1', 'mc_ProfilCalonCTRL@svm')->name('svm1');
            route::get('/muet1', 'mc_ProfilCalonCTRL@muet')->name('muet1');
            route::get('/alevel1', 'mc_ProfilCalonCTRL@alevel')->name('alevel1');
            route::get('/ib1', 'mc_ProfilCalonCTRL@ib')->name('ib1');
            route::get('/stpm1', 'mc_ProfilCalonCTRL@stpm')->name('stpm1');
            route::get('/matrik1', 'mc_ProfilCalonCTRL@matrik')->name('matrik1');
            route::get('/stam1', 'mc_ProfilCalonCTRL@stam')->name('stam1');
            route::get('/ausmat1', 'mc_ProfilCalonCTRL@ausmat')->name('ausmat1');
            route::get('/transkrip1', 'mc_ProfilCalonCTRL@transkrip')->name('transkrip1');
            route::get('/diploma1', 'mc_ProfilCalonCTRL@diploma')->name('diploma1');
            route::get('/bmjulai1', 'mc_ProfilCalonCTRL@bmjulai')->name('bmjulai1');
            route::get('/matejulai1', 'mc_ProfilCalonCTRL@matejulai')->name('matejulai1');
            route::get('/sejjulai1', 'mc_ProfilCalonCTRL@sejjulai')->name('sejjulai1');
            route::get('/akreditasi1', 'mc_ProfilCalonCTRL@akreditasi')->name('akreditasi1');
            route::get('/majikan1', 'mc_ProfilCalonCTRL@majikan')->name('majikan1');
        });

    // MODUL : PROFIL CALON (PAPARAN KHAS : PENGURUSAN KPT)
    Route::group(['prefix' => 'semakan'], function () {
        Route::get('/','mc_kpt_ProfilCalonCTRL@index');
        Route::post('/', 'mc_kpt_ProfilCalonCTRL@carian' )->name('pSemakan');
    });

    // MODUL : KEMASKINI KEPUTUSAN SPM (PAPARAN KHAS)
    Route::group(['prefix' => 'semakan_khas'], function () {
        Route::get('/','mc_CarianKhasCTRL@index');
        Route::post('/', 'mc_CarianKhasCTRL@carian')->name('profil.khas.cari');
        Route::post('/simpan/a', 'mc_CarianKhasCTRL@simpan2');
    });

    // MODUL : DATA SMOKU (API DARI JKM)
    Route::group(['prefix' => 'smoku'], function () {
        route::get('/','mc_SmokuCTRL@index');
        Route::post('/', 'mc_SmokuCTRL@oku_carian' )->name('smoku.cari');
    });

    // MODUL : EPROFIL (API DARI GREAT MOHE)
    Route::group(['prefix' => 'eprofiling'], function () {
        route::get('/','mc_Eprofil@index');
        Route::post('/', 'mc_Eprofil@carian_eprofil' )->name('eprofil.cari');
    });


    // ##########################################################################################################################################################################################
    // DIREKTORI UA / AGENSI
    // ##########################################################################################################################################################################################

    Route::group(['prefix' => 'direktori'], function () {

        // MODUL : NAIB CANSELOR
        Route::get('/nc','da_DirektoriAgensiCTRL@index');
        Route::post('/nc', 'da_DirektoriAgensiCTRL@nc_filter' )->name('direktori.cari.nc');
        Route::post('/nc/simpan','da_DirektoriAgensiCTRL@nc_tambah');
        Route::get('/nc/edit_nc/{id}', 'da_DirektoriAgensiCTRL@modal_nc');
        Route::post('/nc/{id}/update','da_DirektoriAgensiCTRL@nc_update');
        Route::get('/nc/exportnc','da_DirektoriAgensiCTRL@exportNC')->name('direktori.export.nc');

        // MODUL : TIMBALAN NAIB CANSELOR
        Route::get('/tnc','da_DirektoriAgensiCTRL@tnc_index');
        Route::post('/tnc', 'da_DirektoriAgensiCTRL@tnc_filter' )->name('direktori.cari.tnc');
        Route::post('/tnc/simpan','da_DirektoriAgensiCTRL@tnc_tambah');
        Route::get('/tnc/edit_tnc/{id}', 'da_DirektoriAgensiCTRL@modal_tnc');
        Route::post('/tnc/{id}/update','da_DirektoriAgensiCTRL@tnc_update');
        Route::get('/tnc/exportnc','da_DirektoriAgensiCTRL@exportTNC')->name('direktori.export.tnc');

        // BPA IPTA / ILKA
        Route::get('/bkpa','da_DirektoriAgensiCTRL@bkpa_index');
        Route::post('/bkpa', 'da_DirektoriAgensiCTRL@bkpa_filter' )->name('direktori.cari.bkpa');
        Route::post('/bkpa/simpan','da_DirektoriAgensiCTRL@bkpa_tambah');
        Route::get('/bkpa/edit_bkpa/{id}', 'da_DirektoriAgensiCTRL@modal_bkpa');
        Route::post('/bkpa/update','da_DirektoriAgensiCTRL@bkpa_update');
        Route::get('/bkpa/exportbkpa','da_DirektoriAgensiCTRL@exportBKPA')->name('direktori.export.bkpa');

        // MODUL : BKPA
        Route::get('/bpa','da_DirektoriAgensiCTRL@bpa_index');
        Route::post('/bpa', 'da_DirektoriAgensiCTRL@bpa_filter' )->name('direktori.cari.bpa');
        Route::post('/bpa/simpan','da_DirektoriAgensiCTRL@bpa_tambah');
        Route::get('/bpa/edit_bpa/{id}', 'da_DirektoriAgensiCTRL@modal_bpa');
        Route::post('/bpa/{id}/update','da_DirektoriAgensiCTRL@bpa_update');
        Route::get('/bpa/exportbpa','da_DirektoriAgensiCTRL@exportBPA')->name('direktori.export.bpa');

        // MODUL : SUB MODUL
        Route::get('/exportall','da_DirektoriAgensiCTRL@exportALL')->name('direktori.export.all');
        route::get('/exportdir', 'da_DirektoriAgensiCTRL@exportDIR')->name('direktori.download.dir');

    });

    // ##########################################################################################################################################################################################
    // UPUINSIGHT
    // ##########################################################################################################################################################################################

    // MODUL : STATISTIK UPUOnline
    Route::group(['prefix' => 'statistik'], function () {
        route::get('/kategori','ui_StatistikCTRL@index');
        route::get('/pilspm','ui_StatistikCTRL@pilspm');
        route::get('/pilstpm','ui_StatistikCTRL@pilstpm');
        route::get('/pilpstlatihan','ui_StatistikCTRL@pililka');
        route::get('/ipta','ui_StatistikCTRL@ipta');
        route::get('/negeri','ui_StatistikCTRL@negeri');
        route::get('/stpmkategori','ui_StatistikCTRL@stpmkategori');
    });

    // MODUL : STATISTIK PERMOHONAN
    Route::group(['prefix' => 'statistik2'], function () {
        route::get('/spm/perdana','ui_Statistik2CTRL@populariti_perdana_spm');
        route::get('/stpm/perdana','ui_Statistik2CTRL@populariti_perdana_stpm');
        route::get('/spm/perdana/kategori','ui_Statistik2CTRL@populariti_kat_perdana_spm');
        route::get('/stpm/perdana/kategori','ui_Statistik2CTRL@populariti_kat_perdana_stpm');
        route::get('/spm/perdana/tawar','ui_Statistik2CTRL@statistik_tawarperdana_spm');
        route::get('/stpm/perdana/tawar','ui_Statistik2CTRL@statistik_tawarperdana_stpm');

        route::get('/spm/rayu','ui_Statistik2CTRL@populariti_rayuan_spm');
        route::get('/stpm/rayu','ui_Statistik2CTRL@populariti_rayuan_stpm');
        route::get('/spm/rayu/kategori','ui_Statistik2CTRL@populariti_kat_rayuan_spm');
        route::get('/stpm/rayu/kategori','ui_Statistik2CTRL@populariti_kat_rayuan_stpm');
        route::get('/spm/rayu/tawar','ui_Statistik2CTRL@statistik_tawarrayuan_spm');
        route::get('/stpm/rayu/tawar','ui_Statistik2CTRL@statistik_tawarrayuan_stpm');

    });

    // SUB MODUL DARI STATISTIK : SENARAI PEMOHON MATRIKULASI
        Route::group(['prefix' => 'matrik'], function () {
            route::get('/','ui_MatrikCTRL@matrik');

            route::get('/mohon/11','ui_MatrikCTRL@show');
            route::get('/mohon/12','ui_MatrikCTRL@show');
            route::get('/mohon/13','ui_MatrikCTRL@show');
            route::get('/mohon/14','ui_MatrikCTRL@show');
            route::get('/mohon/15','ui_MatrikCTRL@show');
            route::get('/mohon/16','ui_MatrikCTRL@show');
            route::get('/mohon/17','ui_MatrikCTRL@show');
            route::get('/mohon/18','ui_MatrikCTRL@show');
            route::get('/mohon/19','ui_MatrikCTRL@show');
            route::get('/mohon/20','ui_MatrikCTRL@show');
            route::get('/mohon/21','ui_MatrikCTRL@show');
            route::get('/mohon/22','ui_MatrikCTRL@show');
            route::get('/mohon/23','ui_MatrikCTRL@show');
            route::get('/mohon/24','ui_MatrikCTRL@show');
            route::get('/mohon/25','ui_MatrikCTRL@show');
            route::get('/mohon/53','ui_MatrikCTRL@show');
            route::get('/mohon/55','ui_MatrikCTRL@show');
            route::get('/mohon/56','ui_MatrikCTRL@show');
            route::get('/mohon/FA','ui_MatrikCTRL@show');
            route::get('/mohon/FB','ui_MatrikCTRL@show');
            route::get('/mohon/T1','ui_MatrikCTRL@show');
            route::get('/mohon/T2','ui_MatrikCTRL@show');
            route::get('/mohon/T3','ui_MatrikCTRL@show');
            route::get('/mohon/T4','ui_MatrikCTRL@show');
			route::get('/mohon/AT','ui_MatrikCTRL@show');


            route::get('/11','ui_MatrikCTRL@belum_mohon');
            route::get('/12','ui_MatrikCTRL@belum_mohon');
            route::get('/13','ui_MatrikCTRL@belum_mohon');
            route::get('/14','ui_MatrikCTRL@belum_mohon');
            route::get('/15','ui_MatrikCTRL@belum_mohon');
            route::get('/16','ui_MatrikCTRL@belum_mohon');
            route::get('/17','ui_MatrikCTRL@belum_mohon');
            route::get('/18','ui_MatrikCTRL@belum_mohon');
            route::get('/19','ui_MatrikCTRL@belum_mohon');
            route::get('/20','ui_MatrikCTRL@belum_mohon');
            route::get('/21','ui_MatrikCTRL@belum_mohon');
            route::get('/22','ui_MatrikCTRL@belum_mohon');
            route::get('/23','ui_MatrikCTRL@belum_mohon');
            route::get('/24','ui_MatrikCTRL@belum_mohon');
            route::get('/25','ui_MatrikCTRL@belum_mohon');
            route::get('/53','ui_MatrikCTRL@belum_mohon');
            route::get('/55','ui_MatrikCTRL@belum_mohon');
            route::get('/56','ui_MatrikCTRL@belum_mohon');
            route::get('/FA','ui_MatrikCTRL@belum_mohon');
            route::get('/FB','ui_MatrikCTRL@belum_mohon');
            route::get('/T1','ui_MatrikCTRL@belum_mohon');
            route::get('/T2','ui_MatrikCTRL@belum_mohon');
            route::get('/T3','ui_MatrikCTRL@belum_mohon');
            route::get('/T4','ui_MatrikCTRL@belum_mohon');
			route::get('/AT','ui_MatrikCTRL@belum_mohon');
        });

    Route::group(['prefix' => 'matrik-asasi'], function () {
        route::get('/','ui_MatrikCTRL@matrik_asasi');

        route::get('/11','ui_MatrikCTRL@senarai_matrik_asasi');
        route::get('/12','ui_MatrikCTRL@senarai_matrik_asasi');
        route::get('/13','ui_MatrikCTRL@senarai_matrik_asasi');
        route::get('/14','ui_MatrikCTRL@senarai_matrik_asasi');
        route::get('/15','ui_MatrikCTRL@senarai_matrik_asasi');
        route::get('/16','ui_MatrikCTRL@senarai_matrik_asasi');
        route::get('/17','ui_MatrikCTRL@senarai_matrik_asasi');
        route::get('/18','ui_MatrikCTRL@senarai_matrik_asasi');
        route::get('/19','ui_MatrikCTRL@senarai_matrik_asasi');
        route::get('/20','ui_MatrikCTRL@senarai_matrik_asasi');
        route::get('/21','ui_MatrikCTRL@senarai_matrik_asasi');
        route::get('/22','ui_MatrikCTRL@senarai_matrik_asasi');
        route::get('/23','ui_MatrikCTRL@senarai_matrik_asasi');
        route::get('/24','ui_MatrikCTRL@senarai_matrik_asasi');
        route::get('/25','ui_MatrikCTRL@senarai_matrik_asasi');
        route::get('/53','ui_MatrikCTRL@senarai_matrik_asasi');
        route::get('/55','ui_MatrikCTRL@senarai_matrik_asasi');
        route::get('/56','ui_MatrikCTRL@senarai_matrik_asasi');
        route::get('/FA','ui_MatrikCTRL@senarai_matrik_asasi');
		route::get('/FB','ui_MatrikCTRL@senarai_matrik_asasi');
        route::get('/T1','ui_MatrikCTRL@senarai_matrik_asasi');
        route::get('/T2','ui_MatrikCTRL@senarai_matrik_asasi');
        route::get('/T3','ui_MatrikCTRL@senarai_matrik_asasi');
        route::get('/T4','ui_MatrikCTRL@senarai_matrik_asasi');
		route::get('/AT','ui_MatrikCTRL@senarai_matrik_asasi');
    });

    // ##########################################################################################################################################################################################
    // TEKNIKAL
    // ##########################################################################################################################################################################################

    // MODUL : UNIK ID BSN
    Route::group(['prefix' => 'teknikal'], function () {

		// UNIK ID (ID BSN)
        route::get('idbsn/spm','tk_TeknikalCTRL@index');
        route::post('idbsn/spm','tk_TeknikalCTRL@unikid_spm')->name('idspm.cari');
        route::get('idbsn/stpm','tk_TeknikalCTRL@index');
        route::post('idbsn/stpm','tk_TeknikalCTRL@unikid_stpm')->name('idstpm.cari');

        route::get('jana/idbsn/spm','tk_TeknikalCTRL@index');
        route::post('jana/idbsn/spm','tk_TeknikalCTRL@jana_unikid_spm')->name('genspm.jana');
        route::post('jana/idbsn/spm/saveInput','tk_TeknikalCTRL@saveFile')->name('simpan.spm.input.file');
        route::post('jana/idbsn/spm/clearInput','tk_TeknikalCTRL@clearFile')->name('simpan.spm.output.file');

        route::get('jana/idbsn/stpm','tk_TeknikalCTRL@index');
        route::post('jana/idbsn/stpm','tk_TeknikalCTRL@jana_unikid_stpm')->name('genstpm.jana');
        route::post('jana/idbsn/stpm/saveInput','tk_TeknikalCTRL@saveFile')->name('simpan.stpm.input.file');
        route::post('jana/idbsn/stpm/clearInput','tk_TeknikalCTRL@clearFile')->name('simpan.stpm.output.file');

		// E-DOKUMEN MANAGEMENT
        route::get('/pengurusan/file','tk_TeknikalCTRL@showFiles');
        route::post('/pengurusan/file', 'tk_TeknikalCTRL@filefilter' )->name('files.cari');
        route::delete('delete/file/{fileName}', 'tk_TeknikalCTRL@deleteFile')->name('files.delete');

        route::get('/pengurusan/folder','tk_TeknikalCTRL@showFolder');
        route::post('/pengurusan/folder', 'tk_TeknikalCTRL@folderfilter' )->name('folder.cari');
        route::delete('delete/folder/{folderName}', 'tk_TeknikalCTRL@deleteFolder')->name('folders.delete');

        route::get('/pengurusan/tahun','tk_TeknikalCTRL@showTahun');
        route::post('/pengurusan/tahun', 'tk_TeknikalCTRL@tahunfilter' )->name('tahun.cari');
        route::delete('delete/tahun/{folderName}', 'tk_TeknikalCTRL@deleteTahun')->name('tahun.delete');

    });


    // ##########################################################################################################################################################################################
    // AKAUN PENGGUNA
    // ##########################################################################################################################################################################################

    // MODUL : AKAUN PENGGUNA (PENGGUNA)
    Route::group(['prefix' => 'pengguna'], function () {
        Route::get('/','ap_PenggunaCTRL@index');
        Route::post('/update','ap_PenggunaCTRL@update');
        Route::get('/ad/{adnokp}/spm/aktif','ap_PenggunaCTRL@aktifspm_ad');
        Route::get('/ad/{adnokp}/spm/batal','ap_PenggunaCTRL@batalspm_ad');
        Route::get('/ad/{adnokp}/stpm/aktif','ap_PenggunaCTRL@aktifstpm_ad');
        Route::get('/ad/{adnokp}/stpm/batal','ap_PenggunaCTRL@batalstpm_ad');

        Route::get('/ad/{adnokp}/upuselect/aktif','ap_PenggunaCTRL@aktifupuselect_ad');
        Route::get('/ad/{adnokp}/upuselect/batal','ap_PenggunaCTRL@batalupuselect_ad');
    });


    // ##########################################################################################################################################################################################
    // LAMAN PENTADBIR
    // ##########################################################################################################################################################################################

    // MODUL : AKAUN PENGGUNA (ADMINISTRATOR)
    Route::group(['prefix' => 'pentadbir'], function () {
        Route::get('/','ap_PentadbirCTRL@index');
        Route::post('/', 'ap_PentadbirCTRL@tadbirfilter' )->name('tadbir.cari');
        Route::post('/role-simpan','ap_PentadbirCTRL@store_role');
        Route::post('/sk_simpan','ap_PentadbirCTRL@sk_store_permission');
        Route::post('/sp_simpan','ap_PentadbirCTRL@sp_store_permission');
        Route::post('/sk_simpan_nec','ap_PentadbirCTRL@sk_nec_store_permission');
        Route::post('/ts_simpan','ap_PentadbirCTRL@ts_store_permission');
        Route::post('/ps_simpan','ap_PentadbirCTRL@ps_store_permission');

        Route::post('/sp_utama_simpan','ap_PentadbirCTRL@sp_utama_store');
        Route::post('/sp_rayu1_simpan','ap_PentadbirCTRL@sp_rayu1_store');
        Route::post('/sp_rayu2_simpan','ap_PentadbirCTRL@sp_rayu2_store');

        Route::post('/unj_rasmi_simpan','ap_PentadbirCTRL@unj_rasmi_store');
        Route::post('/unj_jackup_simpan','ap_PentadbirCTRL@unj_jackup_store');
        Route::post('/unj_rayuan_simpan','ap_PentadbirCTRL@unj_rayuan_store');

        Route::post('/{tadbir}/spengguna','ap_PentadbirCTRL@simpan_pengguna');


        Route::post('/{tadbir}/edit','ap_PentadbirCTRL@update');
        Route::get('/edit_upucentre/{tadbir}', 'ap_PentadbirCTRL@modal_upucentre');
        Route::post('/update_upucentre', 'ap_PentadbirCTRL@update_upucentre');
        Route::get('/edit_upuselect/{tadbir}', 'ap_PentadbirCTRL@modal_upuselect');
        Route::post('/update_upuselect', 'ap_PentadbirCTRL@update_upuselect');
		Route::get('/{tadbir}/upucentre','ap_PentadbirCTRL@upucentre_user');


    });

    // ##########################################################################################################################################################################################
    // RUJUKAN PENTADBIR
    // ##########################################################################################################################################################################################

    // MODUL : RUJUKAN PENTADBIR (LOG)
    Route::group(['prefix' => 'akaun'], function () {
        Route::get('/','rj_RujukanPentadbirCTRL@index');
        Route::post('/', 'rj_RujukanPentadbirCTRL@akaunfilter' )->name('akaun.cari');
        Route::get('/{nokp}/UPUCentre/{mohon}/{peranan}/BORANG_AKAUN_PENGGUNA', 'rj_RujukanPentadbirCTRL@upucentre');
        Route::get('/{nokp}/UPUSelect/{mohon}/{peranan}/BORANG_AKAUN_PENGGUNA', 'rj_RujukanPentadbirCTRL@upuselect');
    });


    // ##########################################################################################################################################################################################
    // ROUTE TAMBAHAN (EMERGENCY CLOSE PAGE ETEMUDUGA & EDOKUMEN)
    // ##########################################################################################################################################################################################

    Route::group(['prefix' => 'akses'], function () {
        route::get('intviu/senarai','PageErrorCTRL@index');
		route::get('intviu/senarai/peraku','PageErrorCTRL@index');
		route::get('intviu/senarai/prajaya','PageErrorCTRL@index');
		route::get('intviu/senarai/kurjaya','PageErrorCTRL@index');

        route::get('edokumen/senarai','PageErrorCTRL@index');
		route::get('edokumen/senarai/keep-in-view','PageErrorCTRL@index');
		route::get('edokumen/senarai/prajaya','PageErrorCTRL@index');
		route::get('edokumen/senarai/kurjaya','PageErrorCTRL@index');
    });

});

// ##########################################################################################################################################################################################
// AJAX ROUTES (NO AUTH REQUIRED FOR TESTING)
// ##########################################################################################################################################################################################

// ##########################################################################################################################################################################################
// ROUTE FOR LOGOUT
// ##########################################################################################################################################################################################

Route::get('/logout', 'HomeController@logout');

// ##########################################################################################################################################################################################
// ROUTE CLEAR CACHE FOR APP
// ##########################################################################################################################################################################################

Route::get('/clear-1', function() {
    Artisan::call('route:clear');
    return "Route is cleared";
});

Route::get('/clear-2', function() {
    Artisan::call('view:clear');
    return "View is cleared";
});

Route::get('/clear-3', function() {
    Artisan::call('cache:clear');
    return "Cache is cleared";
});

Route::get('/clear-4', function() {
    Artisan::call('config:clear');
    return "Config is cleared";
});

Route::get('/clear-5', function() {
    Artisan::call('config:cache');
    return "Config-Cache is cleared";
});



Route::middleware('fullaccess.user')->group(function(){
	Route::middleware(['module.accessibility'])->group(function () {
    		Route::match(['get', 'post'], '/tadbirunjuran/{jenprog}/{phase}', 'r\TadbirUnjurController@index')->where(['jenprog' => '(spm|stpm)', 'phase' => '(rasmi|jackup|rayuan)'])->name('tadbirunjur.landing');
    		Route::post('/tadbirunjuran/revoke', 'r\TadbirUnjurController@revoke_pengesahan')->name('tadbirunjur.revoke.pengesahan');
    		Route::post('/tadbirunjuran/revoke/all', 'r\TadbirUnjurController@revoke_pengesahan_pukal')->name('tadbirunjur.revoke.pengesahan.all');

    		Route::match(['get', 'post'], '/tadbirpusatlatihan', 'r\TadbirPusatLatihanController@create')->name('tadbirpusatlatihan.landing');
    		Route::post('/tadbirpusatlatihan/daftar/simpan', 'r\TadbirPusatLatihanController@store')->name('tadbirpusatlatihan.daftar.simpan');
    		Route::post('/tadbirpusatlatihan/edit', 'r\TadbirPusatLatihanController@edit')->name('tadbirpusatlatihan.edit');
    		Route::post('/tadbirpusatlatihan/edit/simpan', 'r\TadbirPusatLatihanController@update')->name('tadbirpusatlatihan.edit.simpan');

    		Route::match(['get', 'post'], '/tadbirpusatlatihan-program', 'r\TadbirPusatLatihanProgramController@index')->name('tadbirpusatlatihanprogram.landing');
    		Route::post('/tadbirpusatlatihan-program/peta', 'r\TadbirPusatLatihanProgramController@edit')->name('tadbirpusatlatihanprogram.peta');
    		Route::post('/tadbirpusatlatihan-program/peta/simpan', 'r\TadbirPusatLatihanProgramController@update')->name('tadbirpusatlatihanprogram.peta.simpan');
    		Route::post('/tadbirpusatlatihan-program/peta/padam', 'r\TadbirPusatLatihanProgramController@destroy')->name('tadbirpusatlatihanprogram.peta.padam');
    		Route::get('/tadbirpusatlatihan-program/peta/respon', function(){
        		return view('r.tadbirpusatlatihan-program.response');
    		})->name('tadbirpusatlatihanprogram.peta.respon');

    		Route::match(['get', 'post'], '/tadbirediploma', 'r\TadbirEDokumenController@index')->name('tadbirediploma.papar');
    		Route::match(['get', 'post'], '/tadbiretemuduga', 'r\TadbirIntviuController@index')->name('tadbiretemuduga.papar');
    		Route::match(['get', 'post'], '/tadbiretemuduga/peraku', 'r\TadbirIntviuController@senarai_peraku')->name('tadbiretemuduga.peraku');
	});

    	Route::prefix('pusatlatihan')->group(function(){
        	Route::get('/daftar', 'r\HomeController@create')->name('pusatlatihan.daftar');

		Route::post('/daftar', 'r\HomeController@store')->name('pusatlatihan.daftar.simpan');

		Route::post('/edit', 'r\HomeController@edit')->name('pusatlatihan.edit');

            	Route::post('/edit/simpan', 'r\HomeController@update')->name('pusatlatihan.edit.simpan');
    	});

    Route::prefix('kursus')->group(function(){
        Route::get('/senarai', 'r\MapperController@index')->name('kursus.senarai');

        Route::post('/peta', 'r\MapperController@edit')->name('kursus.peta');

        Route::post('/peta/simpan', 'r\MapperController@update')->name('kursus.peta.simpan');

        Route::post('/peta/padam', 'r\MapperController@destroy')->name('kursus.peta.padam');

        Route::get('/peta/respon', function(){
            return view('mapper.response');
        })->name('kursus.peta.respon');
    });

    Route::middleware(['check.jenprog.stpm', 'can:toggle-access-etemuduga'])->group(function () {
       Route::prefix('intviu')->group(function(){
            Route::get('/senarai/{list_type?}', function(){
        	    return view('r.intviu.index');
            })
	    ->name('intviu.senarai');
       });
    });

    Route::middleware(['check.jenprog.stpm', 'can:toggle-access-ediploma'])->group(function () {
        Route::prefix('edokumen')->group(function(){
            Route::get('/senarai/{list_type?}', function(){
                return view('r.edokumen.index');
            })->name('edokumen.senarai');

            Route::get('/papar/{nokp}/{filename}', 'r\EDokumenController@viewSingleDocument')->name('lw.viewdocument');

            Route::post('/download/bulk', 'r\EDokumenController@initiateBulkDocumentDownload')->name('edokumen.bulk.download');

            Route::get('/trigger-pukal/{filename?}', function($filename){
                return view('r.edokumen.bulk-fetcher', ['filename' => $filename]);
            })->name('ediploma.trigger');
        });
    });

    Route::middleware(['vincenzo'])->group(function () {
         Route::get('/api/smoku', function(){
             return view('r.api.smoku');
         });
    });

    Route::middleware(['auth', 'can:admin'])->group(function () {
    	Route::post('/admin/toggle-module', [ToggleModuleAccess::class, 'toggleModule'])->name('admin.toggle.module');
        Route::post('/admin/toggle-mpb-data', [ToggleModuleAccess::class, 'toggleMpbData'])->name('admin.toggle.mpb.data');
    });
});

Route::middleware(['partialaccess.user', 'check.jenprog.stpm'])->group(function () {
    Route::prefix('edokumen')->group(function(){
        Route::prefix('fakulti')->group(function(){
            Route::get('/senarai/{list_type?}', function(){
                return view('r.edokumen.index');
            });

            Route::get('/papar/{nokp}/{filename}', 'r\EDokumenController@viewSingleDocument')->name('fakulti.viewdocument');

            Route::post('/download/bulk', 'r\EDokumenController@initiateBulkDocumentDownload')->name('fakulti.bulkdownload');

            Route::get('/trigger-pukal/{filename?}', function($filename){
                return view('r.edokumen.bulk-fetcher', ['filename' => $filename]);
            })->name('fakulti.bulkfetcher');
        });
    });
});


    // MODUL : STATISTIK MPB/MPBR DASHBOARD
    Route::group(['prefix' => 'statistik-mpb'], function () {
        route::get('/','ui_StatistikMPBCTRL@dashboard');
        route::get('/dashboard','ui_StatistikMPBCTRL@dashboard');
        route::get('/pencapaian','ui_StatistikMPBCTRL@pencapaian');
        route::get('/pencapaian-stpm','ui_StatistikMPBSTPMCTRL@pencapaianSTTP'); // New STPM-specific pencapaian page
        route::get('/pencapaian-stpm-bidang','ui_StatistikMPBSTPMBidangCTRL@pencapaianSTPMBidang'); // New STPM-specific pencapaian bidang page
        route::get('/pencapaian-spm-bidang','ui_StatistikMPBSPMBidangCTRL@pencapaianSPMBidang'); // New SPM-specific pencapaian peringkat page
        route::get('/pencapaian-spm-analisis','ui_StatistikMPBSPMAnalisisCTRL@pencapaianSPMAnalisis'); // New SPM-specific pencapaian analisis page


        route::get('/unjuran','ui_StatistikMPBCTRL@unjuran');
        route::get('/export-data','ui_StatistikMPBExportCTRL@index')->name('mpb.export.index');
        route::post('/export-download','ui_StatistikMPBExportCTRL@downloadIPTAData')->name('mpb.export.download');

    });



Route::get('/abs', function () {
    // Deprecated debug route
    return response('Debug route disabled', 410);
});

Route::get('/rresyarat/{userid}/{password}/{kodprogram}/{jenprog}/{kategori}','r\UPUSelectSyaratKhasController@index');

// External syarat functionality removed - now using local database only

Route::middleware(['auth'])->group(function () {
    Route::post('/profil', 'r\ProfilCalonController@show')->name('profil.cari');

    // AJAX route for loading syarat program
    Route::post('/load-syarat-program', 'SyaratProgramCTRL@loadSyaratProgram');

    Route::post('/intviu/logout', 'IntviuAuthController@logout')->name('intviu.logout');
});



