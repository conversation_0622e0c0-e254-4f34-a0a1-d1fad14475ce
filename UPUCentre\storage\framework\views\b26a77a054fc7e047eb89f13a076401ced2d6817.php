<?php $__currentLoopData = $syaratkhas_nn; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $syarat_khas_nn): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <?php if(substr($syarat_khas_nn->KODSUBJEK_1,0,1)!='K'): ?>
        <li style="padding-left: .3em;"> 
            Mendapat sekurang-kurangnya Gred <b><?php echo e($syarat_khas_nn->MINGRED); ?></b> dalam mata pelajaran 
            <b>
                <?php echo e(ucwords(strtolower($syarat_khas_nn->KODSUBJEK_2))); ?>.
            </b>
        </li>
    <?php endif; ?>

    <?php if(substr($syarat_khas_nn->KODSUBJEK_1,0,1)=='K'): ?>
        <li style="padding-left: .3em;"> 
            Mendapat sekurang-kurangnya Gred <b><?php echo e($syarat_khas_nn->MINGRED); ?></b> dalam <b><?php echo e($syarat_khas_nn->KET_JUMLAH_MIN_SUBJEK); ?> (<?php echo e($syarat_khas_nn->JUMLAH_MIN_SUBJEK); ?>)</b> mata pelajaran berikut :
            <div class="card bg-light text-dark">
                <div class="card-body p-2">
                    

                    <div class="col-md-12">
                        <span  style="display:table-cell;">&#9679;</span>
                        <span style="padding-left:5px; display:table-cell;"><?php echo e(ucwords(strtolower($syarat_khas_nn->KODSUBJEK_2))); ?></span>
                    </div>

                </div>
            </div>
        </li>
    <?php endif; ?>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

<?php /**PATH C:\xampp\htdocs\UPUCentre\resources\views/es_syaratkhas/spm/papar_syarat/syarat_khas_nn.blade.php ENDPATH**/ ?>