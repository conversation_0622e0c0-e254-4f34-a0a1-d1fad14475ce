<?php

namespace App\Http\Controllers\es_syarat\stpm;
use App\Http\Controllers\Controller;
use App\TawarKod;
use App\SyaratKhas;
use Illuminate\Http\Request;
use Auth;
use DB;
use PDF;
use Illuminate\Support\Arr;
use Cache;
class es_SyaratKhasCTRL extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    } 

    public function index(Request $request)
    {
        $codeset_ipta = DB::connection('upucodeset')->table('refemas_institusi')->whereNotIn('kodipta',['1','2'])->orderBy('sorting', 'ASC')->get();
        $codeset_peringkat = DB::connection('upucodeset')->table('refemas_peringkat')->get();
        $codeset_bidang_nec = DB::connection('upucodeset')->table('refdip_bidangnec')->get();
        $codeset_oku = DB::connection('upucodeset')->table('refperibadi_jencacat')->get();
        $codeset_muet = DB::connection('upucodeset')->table('refmuet_thp')->get();
        $codeset_muet2 = DB::connection('upucodeset')->table('refmuet_thp_2')->get();
        $codeset_tstam = DB::connection('upucodeset')->table('refstam_thp')->get();

        // REQUEST URL
        $url=$request->url(); // SHORT PATH
        $fullUrl=$request->fullUrl(); // FULL PATH

        if($url==$fullUrl)
        {
            // CLEAR SESSION WHEN SAME URL PATH
            session()->forget(['program', 'ipta', 'kategori']); 
        }

        if(session()->get('login_jenprog')=='stpm')
        {
            $sessionSesi = session()->get('sesi_semasa');
            
            // $kategori = $request->input('KATEGORI');
            if(session()->get('login_jenprog')=='stpm')
            {
             if(session()->get('kategori') =='stpm') { $kat= ['A','S']; } 
             elseif(session()->get('kategori') =='matrik') { $kat= ['P','L','U','N','K','J','M','V']; } 
             elseif(session()->get('kategori') =='stam') { $kat= ['T']; } 
             elseif(session()->get('kategori') =='dipG') { $kat= ['G']; } 
             elseif(session()->get('kategori') =='dipE') { $kat= ['E']; } 
             elseif(session()->get('kategori') =='dipF') { $kat= ['F']; } 
             else { $kat= ['A','S']; }
            //  else { $kat= ['A','S','P','L','U','N','K','J','M','V','T','G','E','F']; } 
            }

			$program = DB::connection('emas')->table('program_stpm')
			->whereIn('KATEGORI', $kat)
			->where('STATUS_TAWAR','Y')
			->where('sesi',$sessionSesi)
			->when((Auth::user()->ipta=='11' || Auth::user()->ipta=='22') &&  session()->get('ipta') != '', function ($q) {
				return $q->where('KODPROGRAM_PAPAR', 'like', session()->get('ipta').'%')
						 ->where('KODPROGRAM_PAPAR', 'like', session()->get('program').'%');
			})
			->when((Auth::user()->ipta=='11' || Auth::user()->ipta=='22') && session()->get('ipta') == '', function ($q) {
				return $q->where('KODPROGRAM_PAPAR', 'like', 'UA%')
						 ->where('KODPROGRAM_PAPAR', 'like', session()->get('program').'%');
			})   
			->when((Auth::user()->ipta!='11' && Auth::user()->ipta!='22'), function ($q) {
				return $q->where('KODPROGRAM_PAPAR', 'like', Auth::user()->ipta.'%')
						 ->where('KODPROGRAM_PAPAR', 'like', session()->get('program').'%');
			})
			->orderby('KODPROGRAM_PAPAR','ASC')
			->orderby('KATEGORI','ASC')
			->orderby('ALIRAN','ASC')
			->paginate(10); 

			$kod_program = DB::connection('emas')->table('program_stpm')
			->where('LEPASAN',session()->get('login_jenprog'))
			->where('sesi',$sessionSesi)
			->where('STATUS_TAWAR','Y')
			->when((Auth::user()->ipta=='11' || Auth::user()->ipta=='22') &&  session()->get('ipta') != '', function ($q) {
				return $q->where('KODPROGRAM_PAPAR', 'like', session()->get('ipta').'%');
			})
			->when((Auth::user()->ipta=='11' || Auth::user()->ipta=='22') && session()->get('ipta') == '', function ($q) {
				return $q->where('KODPROGRAM_PAPAR', 'like', 'UA%');
			})   
			->when((Auth::user()->ipta!='11' && Auth::user()->ipta!='22'), function ($q) {
				return $q->where('KODPROGRAM_PAPAR', 'like', Auth::user()->ipta.'%');
			})
			->groupby('KODPROGRAM_PAPAR')
			->get();

        }

        // TAMBAHAN UNTUK SYARAT KHAS BARU
        $skgred = DB::connection('upucodeset')->table('refspm_gred')->whereNotIn('kodspmgred',['R','T','X','Z'])->get();
        $sksubjek = DB::connection('upucodeset')->table('refspm_subjek')->whereNotIn('kodsubjekspm',['0000'])->get();

        if(in_array(Auth::user()->roles, ['1','2','3','4','5','6','7','8','9']))
        { return view('es_syaratkhas.stpm.index',compact('program','kod_program','codeset_oku','sksubjek',
            'codeset_muet','codeset_muet2','codeset_tstam')); }
        else { return view('lock.index'); } 
		
		// return view('lock.selengara');
    }

    public function showSyaratKhas($kodProgram)
    {
        // Get the current session 'sesi_semasa'
        $sesiSemasa = session()->get('sesi_semasa');

        // Query the database
        $syaratkhas_nn_stpm = DB::connection('emas')
            ->table('upuplus_papar_syarat_nn_stpm')
            ->where('PROGRAMKOD', $kodProgram)
            ->where('SESI', $sesiSemasa)
            ->orderBy('ORDERID', 'ASC')
            ->get();

        // Return the result (you can return as JSON or pass to a view)
        return view('syaratkhas.index', compact('syaratkhas_nn_stpm'));
    }


    public function khasfilter(Request $request)
    {
        if ($request->has('khas_cari')) 
        { 
            $program = $request->input('PROGRAM');
            $ipta = $request->input('IPTA');
            $kategori = $request->input('KATEGORI');
            $pendidikan = $request->input('DIDIK1');
            $ketidakupayaan = $request->input('CACAT1');

            // $jenprog = $request->input('JENPROG');

            // INJECT REQUEST TO SESSION AND WILL BE USE TO MANIPULATE IN DATA TABLE
            $request->session()->put(['program' => $program, 'ipta' => $ipta, 'kategori' => $kategori, 'pendidikan' => $pendidikan, 'ketidakupayaan' => $ketidakupayaan]);
        }

        if ($request->has('khas_reset')) 
        { 
            // CLEAR SESSION WHEN CLICK RESET BUTTON
            session()->forget(['program', 'ipta', 'kategori', 'pendidikan', 'ketidakupayaan']); 
        }

        return redirect('stpm/esyaratkhas?page=1');
    }

    public function edit(Request $request, $sk_kodprogram)
    {

        // PERLU TUKAR MENGIKUT KESESUAIAN UNTUK KEMASKINI
        // FLAG UNTUK FASA KEMASKINI
        // 1 = BUKN FASA TAFSIRAN 2 = FASA SEMASA/SELEPAS TAFSIRAN
        $fasa_kemaskini = '2';

        $codeset_ipta = DB::connection('upucodeset')->table('refemas_institusi')->whereNotIn('kodipta',['1','2'])->orderBy('sorting', 'ASC')->get();
        $codeset_peringkat = DB::connection('upucodeset')->table('refemas_peringkat')->get();
        $codeset_bidang_nec = DB::connection('upucodeset')->table('refalldip_bidangnec')->get();
        $codeset_syarat_kesetaraan = DB::connection('emas')->table('syarat_kesetaraan')->get();
        $codeset_stam = DB::connection('upucodeset')->table('refstam_thp')->get();
        $codeset_muet1 = DB::connection('upucodeset')->table('refmuet_thp')->whereNotIn('kodthpmuet',['0','B','T','X'])->get();
        $codeset_muet2 = DB::connection('upucodeset')->table('refmuet_thp_2')->where('statusthpmuet','Y')->get();

        // TAMBAHAN UNTUK SYARAT KHAS BARU
        $skgred = DB::connection('upucodeset')->table('refspm_upuplus_gred')->orderby('sorting','ASC')->get();
		$skgred_stpm = DB::connection('upucodeset')->table('refstpm_upuplus_gred')->orderby('sorting','ASC')->get();
        $skgred_stam = DB::connection('upucodeset')->table('refstam_thp')->whereNotIn('statusthpstam', ['T'])->orderby('sorting','ASC')->get();

        include(app_path() . '/Http/Controllers/es_syarat/stpm/db_view/subjek_kesetaraan_spm.php');
        include(app_path() . '/Http/Controllers/es_syarat/stpm/db_view/subjek_kesetaraan_stpm.php');

        $sksubjek = collect($subjek_kesetaraan_spm);
        $sksubjek_stpm = collect($subjek_kesetaraan_stpm);

        $edit_syaratkhas = DB::connection('emas')->table('program_stpm')->where('LEPASAN',session()->get('login_jenprog'))
        ->where('sesi',session()->get('sesi_semasa'))
        ->where('KODPROGRAM', $sk_kodprogram)
        ->get();  
        
        return view('es_syaratkhas.stpm.update', compact('edit_syaratkhas','codeset_ipta','codeset_peringkat','codeset_bidang_nec','skgred','sksubjek','codeset_syarat_kesetaraan','codeset_stam',
        'sksubjek_stpm','skgred_stam','skgred_stpm','codeset_muet1','codeset_muet2','fasa_kemaskini')); 
    }

    public function editdip(Request $request, $sk_kodprogram, $sk_jensetaraf)
    {

        // PERLU TUKAR MENGIKUT KESESUAIAN UNTUK KEMASKINI
        // FLAG UNTUK FASA KEMASKINI
        // 1 = FASA TAFSIRAN 2 = BUKAN FASA TAFSIRAN
        $fasa_kemaskini = '1';

        $codeset_ipta = DB::connection('upucodeset')->table('refemas_institusi')->whereNotIn('kodipta',['1','2'])->orderBy('sorting', 'ASC')->get();
        $codeset_peringkat = DB::connection('upucodeset')->table('refemas_peringkat')->get();
        $codeset_bidang_nec = DB::connection('upucodeset')->table('refalldip_bidangnec')->get();
        $codeset_syarat_kesetaraan = DB::connection('emas')->table('syarat_kesetaraan')->get();
        $codeset_stam = DB::connection('upucodeset')->table('refstam_thp')->get();
        $codeset_muet1 = DB::connection('upucodeset')->table('refmuet_thp')->whereNotIn('kodthpmuet',['0','B','T','X'])->get();
        $codeset_muet2 = DB::connection('upucodeset')->table('refmuet_thp_2')->where('statusthpmuet','Y')->get();

        // TAMBAHAN UNTUK SYARAT KHAS BARU
        $skgred = DB::connection('upucodeset')->table('refspm_upuplus_gred')->orderby('sorting','ASC')->get();
        $skgred_stam = DB::connection('upucodeset')->table('refstam_thp')->orderby('sorting','ASC')->get();
 
        $edit_syaratkhas = DB::connection('emas')->table('program_stpm')->where('LEPASAN',session()->get('login_jenprog'))
        ->where('sesi',session()->get('sesi_semasa'))
        ->where('KODPROGRAM', $sk_kodprogram)
        ->where('JENSETARAF', $sk_jensetaraf)
        ->get();  

        return view('es_syaratkhas.stpm.update_diploma', compact('edit_syaratkhas','codeset_ipta','codeset_peringkat','codeset_bidang_nec','skgred','codeset_syarat_kesetaraan','codeset_stam',
        'skgred_stam','codeset_muet1','codeset_muet2','fasa_kemaskini')); 
    }

    public function update_dip(Request $request)
    {
        $st_fasa = $request->input('fasa');
        $st_id = $request->input('id');
        $st_program = $request->input('program');
        $st_kategori = $request->input('kategori');
        $st_jensetaraf = $request->input('jensetaraf');
        $st_subjek = $request->input('STSUBJEK');
        $st_gred = $request->input('STGRED');
        $st_susun = $request->input('susunid');
        $st1_subjek = $request->input('ST1SUBJEK');
        $st1_gred = $request->input('ST1GRED');
        $st1_susun = $request->input('st1susunid');
        $st_catat = $request->input('catatan');
        $st_syarat= $request->input('syarat');
		
        $sl_pngk = $request->input('SL_PNGK');
        if($sl_pngk!='') { $pngk='1'; } else { $pngk='0'; }
        
        $sl_tstam = $request->input('SL_TSTAM');
        if($sl_tstam!='') { $tstam='1'; } else { $tstam='0'; }

        $sl_band1 = $request->input('SL_BAND1');
        $sl_band2 = $request->input('SL_BAND2');

        if($sl_band1!='' || $sl_band2!='') { $allband='1'; } else { $allband='0'; }
        
        $sl_tahun1 = $request->input('SL_TAHUN1');
        $sl_tahun2 = $request->input('SL_TAHUN2');

        $sl_bi = $request->input('SL_BI');
        if($sl_bi!='') { $bi_1119='1'; } else { $bi_1119='0'; }

        $sl_kahwin = $request->input('SL_KAHWIN');
        if($sl_kahwin=='B') { $kahwin='1'; } else { $kahwin='0'; }

        $sl_jantina = $request->input('SL_JANTINA');
      
        if($sl_jantina=='L' || $sl_jantina=='P') { $jantina='1'; } else { $jantina='0'; }

        $sl_opumur = $request->input('opumur');
        if($sl_opumur == 'T') { $opumur='0'; } else { $opumur='1'; }

        $sl_umur = $request->input('umur');
        $sl_bulan = $request->input('bulan');

        $total_bulan = (($sl_umur*12) + $sl_bulan);

        $sl_umur1 = $request->input('umur1');
        $sl_bulan1 = $request->input('bulan1');

        $total_bulan1 = (($sl_umur1*12) + $sl_bulan1);

        $sl_3m = $request->input('SL_3M');

        $valid_oku = \DB::connection('emas')->table('program_stpm')->select('OKU')->where('KODPROGRAM', $st_id)->where('LEPASAN','STPM')->where('sesi', session()->get('sesi_semasa'))->get();

        if(!empty($valid_oku[0]) && ($valid_oku[0]->OKU=='T' || $valid_oku[0]->OKU=='N')) {$oku='0';} elseif(!empty($valid_oku[0]) && $valid_oku[0]->OKU=='Y') { $oku='1'; } else { $oku='0'; }
        
        $count_syarat_lain = \DB::connection('emas')->table('syarat_lain')->where('Programkod', $st_id)->where('Kategori', $st_jensetaraf)->where('sesi', session()->get('sesi_semasa'))->get();
        $valid_syarat_lain = count($count_syarat_lain);
        

        if($valid_syarat_lain > 0)
        {
            $update_syarat_lain = DB::connection('emas')
            ->table('syarat_lain')
            ->where('Programkod', $st_id)
            ->where('Kategori', $st_jensetaraf)
            ->where('sesi', session()->get('sesi_semasa'))
            ->update(
                [
                    'Syarat_PNGK_Diploma' => $pngk, 
                    'PNGK_Diploma' => $sl_pngk, 
                    'Syarat_MUET' => $allband, 
                    'MUET1_BAND' => $sl_band1, 
                    'MUET1_Tahun' => $sl_tahun1, 
                    'MUET2_Band' => $sl_band2, 
                    'MUET2_Tahun' => $sl_tahun2, 
                    'Syarat_1119' => $bi_1119, 
                    'gred_1119' => $sl_bi, 
                    'Syarat_taraf_perkahwinan' => $kahwin, 
                    'Taraf_perkahwinan' => $sl_kahwin, 
                    'Syarat_Jantina' => $jantina, 
                    'Jantina' => $sl_jantina, 
                    'Syarat_Umur' => $opumur, 
                    'Operasi_Umur' => $sl_opumur, 
                    'Umur1' => $sl_umur, 
                    'bulan1' => $sl_bulan, 
                    'umur_bulan1' => $total_bulan, 
                    'Umur2' => $sl_umur1 , 
                    'bulan2' =>  $sl_bulan1, 
                    'umur_bulan2' =>  $total_bulan1, 
                    'Syarat_Kecacatan' =>  $oku, 
                    'syarat_3M' =>  $sl_3m, 
                    'fasa' => $st_fasa,
                    'updated_at' => now()
                ]);
        }
        else
        {
            $insert_syarat_lain = DB::connection('emas')
            ->table('syarat_lain')
            ->insert(
                [
                    'Programkod' => $st_id , 
                    'Kategori' => $st_jensetaraf , 
                    'Syarat_PNGK_Diploma' => $pngk, 
                    'PNGK_Diploma' => $sl_pngk, 
                    'Syarat_MUET' => $allband, 
                    'MUET1_BAND' => $sl_band1, 
                    'MUET1_Tahun' => $sl_tahun1, 
                    'MUET2_Band' => $sl_band2, 
                    'MUET2_Tahun' => $sl_tahun2, 
                    'Syarat_1119' => $bi_1119, 
                    'gred_1119' => $sl_bi, 
                    'Syarat_taraf_perkahwinan' => $kahwin , 
                    'Taraf_perkahwinan' => $sl_kahwin, 
                    'Syarat_Jantina' => $jantina, 
                    'Jantina' => $sl_jantina, 
                    'Syarat_Umur' => $opumur, 
                    'Operasi_Umur' => $sl_opumur, 
                    'Umur1' => $sl_umur, 
                    'bulan1' => $sl_bulan, 
                    'umur_bulan1' => $total_bulan, 
                    'Umur2' => $sl_umur1 , 
                    'bulan2' =>  $sl_bulan1, 
                    'umur_bulan2' =>  $total_bulan1, 
                    'Syarat_Kecacatan' =>  $oku, 
                    'syarat_3M' =>  $sl_3m, 
                    'fasa' => $st_fasa,
                    'sesi' =>  session()->get('sesi_semasa'), 
                    'updated_at' => now()
                ]);  
        }

        $update_catatan = DB::connection('emas')
        ->table('program_stpm')
        ->where('KODPROGRAM', $st_id)
        ->where('JENSETARAF', $st_jensetaraf)
        ->where('LEPASAN','stpm')
        ->where('sesi', session()->get('sesi_semasa'))
        ->update(
            [
                'REMARKS' => trim($st_catat)
            ]);


        $count_syarat_dip = \DB::connection('emas')->table('syarat_khas_diploma')->where('PROGRAMKOD', $st_id)->where('KATEGORI', $st_kategori)->where('JENSETARAF', $st_jensetaraf)->where('SESI', session()->get('sesi_semasa'))->get();
        $valid_syarat_dip = count($count_syarat_dip);

        if($valid_syarat_dip > 0)
        {
            $update_syarat = DB::connection('emas')
            ->table('syarat_khas_diploma')
            ->where('PROGRAMKOD', $st_id)
            ->where('KATEGORI', $st_kategori)
            ->where('JENSETARAF', $st_jensetaraf)
            ->where('SESI', session()->get('sesi_semasa'))
            ->update(
                [
                    'SYARAT' => $st_syarat,
                    'FASA' => $st_fasa,
                    'UPDATED_AT' => now()
                ]);
        }
        else
        {
            $update_syarat = DB::connection('emas')
            ->table('syarat_khas_diploma')
            ->insert(
                [
                    'PROGRAMKOD' => $st_id , 
                    'KATEGORI' => $st_kategori , 
                    'JENSETARAF' => $st_jensetaraf, 
                    'SESI' => session()->get('sesi_semasa'), 
                    'SYARAT' => $st_syarat,
                    'FASA' => $st_fasa,
                    'UPDATED_AT' => now()
                ]);
        }

         return redirect('stpm/esyaratkhas?page=1')->with('success', 'Syarat Khas bagi Program ' .$st_id. ' telah dikemaskini.'); 
        // return back()->with('success', 'Syarat Khas bagi Program' .$st_id. ' telah dikemaskini.');

    }


    public function update(Request $request)
    {
        $st_fasa = $request->input('fasa');
        $st_id = $request->input('id');
        $st_program = $request->input('program');
        $st_kategori = $request->input('kategori');

        $st_subjek = $request->input('STSUBJEK');
        $st_gred = $request->input('STGRED');
        $st_susun = $request->input('susunid');

        $st1_subjek = $request->input('ST1SUBJEK');
        $st1_gred = $request->input('ST1GRED');
        $st1_susun = $request->input('st1susunid');

        $st_catat = $request->input('catatan');

        // SYARAT KUMPULAN 1
        $sk_susun_1 = $request->input('susun_kump1');
        $sk_subjek_1 = $request->input('SKSUBJEK1');
        $sk_jum1 = $request->input('SK_JUM1');
        $sk_jum2 = $request->input('SK_JUM2');
        $sk_gred1_kump1 = $request->input('SKGRED1_KUMP1');
        $sk_gred2_kump1 = $request->input('SKGRED2_KUMP1');
        $susun_subjekG1 = $request->input('susun_subjekG1');

        // SYARAT KUMPULAN 2
        $sk_susun_2 = $request->input('susun_kump2');
        $sk_subjek_2 = $request->input('SKSUBJEK2');
        $sk_jum3 = $request->input('SK_JUM3');
        $sk_jum4 = $request->input('SK_JUM4');
        $sk_gred1_kump2 = $request->input('SKGRED1_KUMP2');
        $sk_gred2_kump2 = $request->input('SKGRED2_KUMP2');
        $susun_subjekG2 = $request->input('susun_subjekG2');

        // SYARAT KUMPULAN 3
        $sk_susun_3 = $request->input('susun_kump3');
        $sk_subjek_3 = $request->input('SKSUBJEK3');
        $sk_jum5 = $request->input('SK_JUM5');
        $sk_gred1_kump3 = $request->input('SKGRED1_KUMP3');
        $susun_subjekG3 = $request->input('susun_subjekG3');

        // SYARAT FLEKSIBEL STPM 1
        $sf_susun_1 = $request->input('susun_flexi1');
        $sf_gred1_flexi1 = $request->input('SFGRED_FLEXI1');
        $sf_jum1 = $request->input('SF_JUMF1');
        $sf_syarat_flexi1 = $request->input('SF_XSYARAT1');
        $sf_kepil_1 = $request->input('SF_DIKEPILKAN1');

        // SYARAT FLEKSIBEL STPM 2
        $sf_susun_4 = $request->input('susun_flexi4');
        $sf_gred1_flexi4 = $request->input('SFGRED_FLEXI4');
        $sf_jum4 = $request->input('SF_JUMF4');
        $sf_syarat_flexi4 = $request->input('SF_XSYARAT4');

        // SYARAT FLEKSIBEL 3 STPM
        $sf_susun_5 = $request->input('susun_flexi5');
        $sf_gred1_flexi5 = $request->input('SFGRED_FLEXI5');
        $sf_jum5  = $request->input('SF_JUMF5');
        $sf_subjek_5 = $request->input('SFSUBJEKF5');
        $susun_subjekF5 = $request->input('susun_subjekF5');

        // SYARAT FLEKSIBEL 1
        $sf_susun_2 = $request->input('susun_flexi2');
        $sf_gred1_flexi2 = $request->input('SFGRED_FLEXI2');
        $sf_jum2  = $request->input('SF_JUMF2');
        $sf_syarat_flexi2 = $request->input('SF_XSYARAT2');
        $sf_kepil_2 = $request->input('SF_DIKEPILKAN2');

        // SYARAT FLEKSIBEL 2
        $sf_susun_3 = $request->input('susun_flexi3');
        $sf_gred1_flexi3 = $request->input('SFGRED_FLEXI3');
        $sf_jum3  = $request->input('SF_JUMF3');
        $sf_subjek_1 = $request->input('SFSUBJEKF3');
        $susun_subjekF3 = $request->input('susun_subjekF3');

        $delete_st = DB::connection('emas')
        ->table('syarat_khas_stpm')
		->where('SESI', session()->get('sesi_semasa'))
        ->where('PROGRAMKOD', $st_id)
        ->delete();

        $delete_skumpulan_ = DB::connection('emas')
        ->table('syarat_sub_kumpulan_subjek_stpm')
		->where('SESI', session()->get('sesi_semasa'))
        ->where(DB::raw('SUBSTR(KUMPULAN,3,LENGTH(KUMPULAN))'), $st_id)
        ->delete();

        $delete_skumpulan_x1 = DB::connection('emas')
        ->table('syarat_xsub_kumpulan_subjek_stpm')
		->where('SESI', session()->get('sesi_semasa'))
        ->where(DB::raw('SUBSTR(KUMPULAN,3,LENGTH(KUMPULAN))'), $st_id)
        ->delete();
		
        $delete_skumpulan_x2 = DB::connection('emas')
        ->table('syarat_xsub_kumpulan_subjek_stpm')
		->where('SESI', session()->get('sesi_semasa'))
        ->where(DB::raw('SUBSTR(KUMPULAN,2,LENGTH(KUMPULAN))'), $st_id)
        ->delete();

        $sessionSesi = session()->get('sesi_semasa');
        // SYARAT TUNGGAL STPM / MATRIK / STAM ##############################################################################################################################################################################
		
       if (!empty($st1_gred) && !empty($st1_subjek))
        {
			$count_st1 = count($st1_subjek);
			// if ($count_st1 != 0)
			if ($st1_gred > 0 && $st1_subjek[0] != '')
			{
				if (($st1_gred > 0) && ($st1_subjek[0] != ''))
				{
					for($i = 0; $i<$count_st1; $i++)
					{
						$insert_st = DB::connection('emas')
						->table('syarat_khas_stpm')
						->insert(
							[
								'PROGRAMKOD' => $st_id,
								'KODSUBJEK' => $st1_subjek[$i],
								'MINGRED' => $st1_gred[$i],
								'KUMPULAN' => 'N',
								'SUB_KUMPULAN' => 'N',
								'JUMLAH_MIN_SUBJEK' => '1',
								'KATEGORI' => $st_kategori,
								'ORDERID' => $st1_susun[$i],
                                'SESI' => session()->get('sesi_semasa'),
                                'FASA' => $st_fasa,
								'UPDATED_AT' => now()
							]);
					}

                    // CHECK AND GET VALUE TO CREATE LOG
                    include(app_path() . '/Http/Controllers/es_syarat/stpm/db_view/syaratkhas_nn_stpm.php');
					$count = count($get_syarat_nn_stpm);

					if ($count != 0)
					{
					    for($i = 0; $i<$count; $i++)
					    {
					        $insert_log_syarat = DB::connection('emas')
					        ->table('upu_log_syarat_stpm')
					        ->insert(
					            [
					                'PROGRAMKOD' => $get_syarat_nn_stpm[$i]->PROGRAMKOD,
					                'KODSUBJEK_1' => $get_syarat_nn_stpm[$i]->KODSUBJEK_1,
					                'KODSUBJEK_2' => $get_syarat_nn_stpm[$i]->KODSUBJEK_2,
					                'MINGRED' => $get_syarat_nn_stpm[$i]->MINGRED,
					                'KUMPULAN' => $get_syarat_nn_stpm[$i]->KUMPULAN,
					                'SUB_KUMPULAN' => $get_syarat_nn_stpm[$i]->SUB_KUMPULAN,
					                'KET_JUMLAH_MIN_SUBJEK' => $get_syarat_nn_stpm[$i]->KET_JUMLAH_MIN_SUBJEK,
					                'JUMLAH_MIN_SUBJEK' => $get_syarat_nn_stpm[$i]->JUMLAH_MIN_SUBJEK,
					                'SESI' => $get_syarat_nn_stpm[$i]->SESI,
					                'ORDERID' => $get_syarat_nn_stpm[$i]->ORDERID,
					                'ORDERID2' => $get_syarat_nn_stpm[$i]->ORDERID2,
					                'JENIS_SYARAT' => 'ST',
                                    'FASA' => $st_fasa,
                                    'UPDATED_USER' => Auth::user()->user_id,
                                    'UPDATED_AT' => now()
					            ]);
					    }
					}
				}
			}			
		}


        // SYARAT TUNGGAL SPM ##############################################################################################################################################################################
       if (!empty($st_gred) && !empty($st_subjek))
        {
		
			$count_st = count($st_subjek);
			// if ($st_subjek > 0)

			if ($st_subjek > 0 && $st_subjek[0] != '')
			{
				if (($st_gred > 0) && ($st_subjek[0] != ''))
				{
					for($i = 0; $i<$count_st; $i++)
					{
						$insert_st = DB::connection('emas')
						->table('syarat_khas_stpm')
						->insert(
							[
								'PROGRAMKOD' => $st_id,
								'KODSUBJEK' => $st_subjek[$i],
								'MINGRED' => $st_gred[$i],
								'KUMPULAN' => 'N',
								'SUB_KUMPULAN' => 'N',
								'JUMLAH_MIN_SUBJEK' => '1',
								'KATEGORI' => 'SPM',
								'ORDERID' => $st_susun[$i],
                                'sesi' => session()->get('sesi_semasa'),
                                'FASA' => $st_fasa,
								'UPDATED_AT' => now()
							]);
					}

					// CHECK AND GET VALUE TO CREATE LOG
					include(app_path() . '/Http/Controllers/es_syarat/stpm/db_view/syaratkhas_nn_spm.php');
					$count = count($get_syarat_nn_spm);

					if ($count != 0)
					{
					    for($i = 0; $i<$count; $i++)
					    {
					        $insert_log_syarat = DB::connection('emas')
					        ->table('upu_log_syarat_stpm')
					        ->insert(
					            [
					                'PROGRAMKOD' => $get_syarat_nn_spm[$i]->PROGRAMKOD,
					                'KODSUBJEK_1' => $get_syarat_nn_spm[$i]->KODSUBJEK_1,
					                'KODSUBJEK_2' => $get_syarat_nn_spm[$i]->KODSUBJEK_2,
					                'MINGRED' => $get_syarat_nn_spm[$i]->MINGRED,
					                'KUMPULAN' => $get_syarat_nn_spm[$i]->KUMPULAN,
					                'SUB_KUMPULAN' => $get_syarat_nn_spm[$i]->SUB_KUMPULAN,
					                'KET_JUMLAH_MIN_SUBJEK' => $get_syarat_nn_spm[$i]->KET_JUMLAH_MIN_SUBJEK,
					                'JUMLAH_MIN_SUBJEK' => $get_syarat_nn_spm[$i]->JUMLAH_MIN_SUBJEK,
					                'SESI' => $get_syarat_nn_spm[$i]->SESI,
					                'ORDERID' => $get_syarat_nn_spm[$i]->ORDERID,
					                'ORDERID2' => $get_syarat_nn_spm[$i]->ORDERID2,
                                    'JENIS_SYARAT' => 'ST',
                                    'FASA' => $st_fasa,
                                    'UPDATED_USER' => Auth::user()->user_id,
                                    'UPDATED_AT' => now()
					            ]);
					    }
					}


				}
				
			}
		}


        // SYARAT FLEKSIBEL STPM 1 ##############################################################################################################################################################################

        if (!empty($sf_gred1_flexi1) && !empty($sf_jum1))
        {

            if ($sf_syarat_flexi1 == 'F') // Dalam syarat kumpulan 1
            {
                $sub_Kumpulan ='F'; // Ambil semua walaupun dah dikira
            }
            else if ($sf_syarat_flexi1 == 'X') // Mana2 matapelajaran yang belum dikira
            {
                  $sub_Kumpulan ='X'; 
            }
            else if ($sf_syarat_flexi1 == 'Y') // Mana2 matapelajaran yang belum dikira
            {
                  $sub_Kumpulan ='Y'; 
            }
            else { $sub_Kumpulan ='';  }

            $insert_st = DB::connection('emas')
            ->table('syarat_khas_stpm')
            ->insert(
                [
                    'PROGRAMKOD' => $st_id,
                    'KODSUBJEK' => 'F'.$st_id,
                    'MINGRED' => $sf_gred1_flexi1,
                    'KUMPULAN' => 'F',
                    'SUB_KUMPULAN' => $sub_Kumpulan,
                    'JUMLAH_MIN_SUBJEK' => $sf_jum1,
                    'KATEGORI' => 'SPM',
                    'ORDERID' => $sf_susun_1,
                    'SESI' => session()->get('sesi_semasa'),
                    'FASA' => $st_fasa,
                    'UPDATED_AT' => now()
                ]);

                // CHECK AND GET VALUE TO CREATE LOG
                include(app_path() . '/Http/Controllers/es_syarat/stpm/db_view/syaratkhas_f1.php');
                $countf1 = count($get_syarat_f1);

                if ($countf1 != 0)
                {
                    for($i = 0; $i<$countf1; $i++)
                    {
                        $insert_log_syarat = DB::connection('emas')
                        ->table('upu_log_syarat_stpm')
                        ->insert(
                            [
                                'PROGRAMKOD' => $get_syarat_f1[$i]->PROGRAMKOD,
                                'GKUMPULAN' => $get_syarat_f1[$i]->GKUMPULAN,
                                'KODSUBJEK_1' => $get_syarat_f1[$i]->KODSUBJEK_1,
                                'KODSUBJEK_2' => $get_syarat_f1[$i]->KODSUBJEK_2,
                                'MINGRED' => $get_syarat_f1[$i]->MINGRED,
                                'KUMPULAN' => $get_syarat_f1[$i]->KUMPULAN,
                                'SUB_KUMPULAN' => $get_syarat_f1[$i]->SUB_KUMPULAN,
                                'KET_JUMLAH_MIN_SUBJEK' => $get_syarat_f1[$i]->KET_JUMLAH_MIN_SUBJEK,
                                'JUMLAH_MIN_SUBJEK' => $get_syarat_f1[$i]->JUMLAH_MIN_SUBJEK,
                                'SESI' => $get_syarat_f1[$i]->SESI,
                                'ORDERID' => $get_syarat_f1[$i]->ORDERID,
                                'ORDERID2' => $get_syarat_f1[$i]->ORDERID2,
                                'JENIS_SYARAT' => 'SF1',
                                'FASA' => $st_fasa,
                                'UPDATED_USER' => Auth::user()->user_id,
                                'UPDATED_AT' => now()
                            ]);
                    }
                }  

        }

        // SYARAT FLEKSIBEL STPM 2 ##############################################################################################################################################################################
        if (!empty($sf_gred1_flexi4) && !empty($sf_jum4))
        {

            if ($sf_syarat_flexi4 == 'F') // Dalam syarat kumpulan 1
            {
                $sub_Kumpulan ='F'; // Ambil semua walaupun dah dikira
            }
            else if ($sf_syarat_flexi4 == 'X') // Mana2 matapelajaran yang belum dikira
            {
                  $sub_Kumpulan ='X'; 
            }
            else if ($sf_syarat_flexi4 == 'Y') // Mana2 matapelajaran yang belum dikira
            {
                  $sub_Kumpulan ='Y'; 
            }
            else { $sub_Kumpulan ='';  }

            $insert_st = DB::connection('emas')
            ->table('syarat_khas_stpm')
            ->insert(
                [
                    'PROGRAMKOD' => $st_id,
                    'KODSUBJEK' => 'F4'.$st_id,
                    'MINGRED' => $sf_gred1_flexi4,
                    'KUMPULAN' => 'F4',
                    'SUB_KUMPULAN' => $sub_Kumpulan,
                    'JUMLAH_MIN_SUBJEK' => $sf_jum4,
                    'KATEGORI' => $st_kategori,
                    'ORDERID' => $sf_susun_1,
                    'SESI' => session()->get('sesi_semasa'),
                    'FASA' => $st_fasa,
                    'UPDATED_AT' => now()
                ]);


                // CHECK AND GET VALUE TO CREATE LOG
                include(app_path() . '/Http/Controllers/es_syarat/stpm/db_view/syaratkhas_f4.php');
                $countf4 = count($get_syarat_f4);

                if ($countf4 != 0)
                {
                    for($i = 0; $i<$countf4; $i++)
                    {
                        $insert_log_syarat = DB::connection('emas')
                        ->table('upu_log_syarat_stpm')
                        ->insert(
                            [
                                'PROGRAMKOD' => $get_syarat_f4[$i]->PROGRAMKOD,
                                'GKUMPULAN' => $get_syarat_f4[$i]->GKUMPULAN,
                                'KODSUBJEK_1' => $get_syarat_f4[$i]->KODSUBJEK_1,
                                'KODSUBJEK_2' => $get_syarat_f4[$i]->KODSUBJEK_2,
                                'MINGRED' => $get_syarat_f4[$i]->MINGRED,
                                'KUMPULAN' => $get_syarat_f4[$i]->KUMPULAN,
                                'SUB_KUMPULAN' => $get_syarat_f4[$i]->SUB_KUMPULAN,
                                'KET_JUMLAH_MIN_SUBJEK' => $get_syarat_f4[$i]->KET_JUMLAH_MIN_SUBJEK,
                                'JUMLAH_MIN_SUBJEK' => $get_syarat_f4[$i]->JUMLAH_MIN_SUBJEK,
                                'SESI' => $get_syarat_f4[$i]->SESI,
                                'ORDERID' => $get_syarat_f4[$i]->ORDERID,
                                'ORDERID2' => $get_syarat_f4[$i]->ORDERID2,
                                'JENIS_SYARAT' => 'SF4',
                                'FASA' => $st_fasa,
                                'UPDATED_USER' => Auth::user()->user_id,
                                'UPDATED_AT' => now()
                            ]);
                    } 
                } 

                

        }

        // SYARAT FLEKSIBEL 3 STPM ##########################################################

        if (($sf_subjek_5 > 0) && ($sf_subjek_5[0] != '') && ($sf_jum5 != '') && ($sf_gred1_flexi5 != ''))
        { 
            $insert_st = DB::connection('emas')
            ->table('syarat_khas_stpm')
            ->insert(
                [
                    'PROGRAMKOD' => $st_id,
                    'KODSUBJEK' => 'F5'.$st_id,
                    'MINGRED' => $sf_gred1_flexi5,
                    'KUMPULAN' => 'F5',
                    'SUB_KUMPULAN' => 'F5',
                    'JUMLAH_MIN_SUBJEK' => $sf_jum5,
                    'KATEGORI' => $st_kategori,
                    'ORDERID' => $sf_susun_5,
                    'SESI' => session()->get('sesi_semasa'),
                    'FASA' => $st_fasa,
                    'UPDATED_AT' => now()
                ]);
            
            for ($i = 0; $i < count($sf_subjek_5); $i++) 
            {
                $insert_st = DB::connection('emas')
                ->table('syarat_xsub_kumpulan_subjek_stpm')
                ->insert(
                    [
                        'KUMPULAN' => 'F5'.$st_id,
                        'KODSUBJEK' => $sf_subjek_5[$i],
                        'SESI' => session()->get('sesi_semasa'),
                        'ORDERID' => $susun_subjekF5[$i],
                    ]); 
            }


                // CHECK AND GET VALUE TO CREATE LOG
                include(app_path() . '/Http/Controllers/es_syarat/stpm/db_view/syaratkhas_f5.php');
                $countf5 = count($get_syarat_f5);

                if ($countf5 != 0)
                {
                    for($i = 0; $i<$countf5; $i++)
                    {
                        $insert_log_syarat = DB::connection('emas')
                        ->table('upu_log_syarat_stpm')
                        ->insert(
                            [
                                'PROGRAMKOD' => $get_syarat_f5[$i]->PROGRAMKOD,
                                'GKUMPULAN' => $get_syarat_f5[$i]->GKUMPULAN,
                                'KODSUBJEK_1' => $get_syarat_f5[$i]->KODSUBJEK_1,
                                'KODSUBJEK_2' => $get_syarat_f5[$i]->KODSUBJEK_2,
                                'MINGRED' => $get_syarat_f5[$i]->MINGRED,
                                'KUMPULAN' => $get_syarat_f5[$i]->KUMPULAN,
                                'SUB_KUMPULAN' => $get_syarat_f5[$i]->SUB_KUMPULAN,
                                'KET_JUMLAH_MIN_SUBJEK' => $get_syarat_f5[$i]->KET_JUMLAH_MIN_SUBJEK,
                                'JUMLAH_MIN_SUBJEK' => $get_syarat_f5[$i]->JUMLAH_MIN_SUBJEK,
                                'SESI' => $get_syarat_f5[$i]->SESI,
                                'ORDERID' => $get_syarat_f5[$i]->ORDERID,
                                'ORDERID2' => $get_syarat_f5[$i]->ORDERID2,
                                'JENIS_SYARAT' => 'SF5',
                                'FASA' => $st_fasa,
                                'UPDATED_USER' => Auth::user()->user_id,
                                'UPDATED_AT' => now()
                            ]);
                    } 
                } 

        }

        // SYARAT FLEKSIBEL 1 SPM ##########################################################

        if (!empty($sf_gred1_flexi2) && !empty($sf_jum2))
        {
            if ($sf_syarat_flexi2 == 'F') // Dalam syarat kumpulan 1
            {
                $sub_Kumpulan2 ='F'; // Ambil semua walaupun dah dikira
            }
            else if ($sf_syarat_flexi2 == 'X') // Mana2 matapelajaran yang belum dikira
            {
                $sub_Kumpulan2 ='X'; 
            }
            else if ($sf_syarat_flexi2 == 'Y') // Mana2 matapelajaran selain diatas
            {
                $sub_Kumpulan2 ='Y'; 
            }

            $insert_st = DB::connection('emas')
            ->table('syarat_khas_stpm')
            ->insert(
                [
                    'PROGRAMKOD' => $st_id,
                    'KODSUBJEK' => 'F2'.$st_id,
                    'MINGRED' => $sf_gred1_flexi2,
                    'KUMPULAN' => 'F2',
                    'SUB_KUMPULAN' => $sub_Kumpulan2,
                    'JUMLAH_MIN_SUBJEK' => $sf_jum2,
                    'KATEGORI' => $st_kategori,
                    'ORDERID' => $sf_susun_2,
                    'SESI' => session()->get('sesi_semasa'),
                    'FASA' => $st_fasa,
                    'UPDATED_AT' => now()
                ]);


                // CHECK AND GET VALUE TO CREATE LOG
                include(app_path() . '/Http/Controllers/es_syarat/stpm/db_view/syaratkhas_f2.php');
                $countf2 = count($get_syarat_f2);

                if ($countf2 != 0)
                {
                    for($i = 0; $i<$countf2; $i++)
                    {
                        $insert_log_syarat = DB::connection('emas')
                        ->table('upu_log_syarat_stpm')
                        ->insert(
                            [
                                'PROGRAMKOD' => $get_syarat_f2[$i]->PROGRAMKOD,
                                'GKUMPULAN' => $get_syarat_f2[$i]->GKUMPULAN,
                                'KODSUBJEK_1' => $get_syarat_f2[$i]->KODSUBJEK_1,
                                'KODSUBJEK_2' => $get_syarat_f2[$i]->KODSUBJEK_2,
                                'MINGRED' => $get_syarat_f2[$i]->MINGRED,
                                'KUMPULAN' => $get_syarat_f2[$i]->KUMPULAN,
                                'SUB_KUMPULAN' => $get_syarat_f2[$i]->SUB_KUMPULAN,
                                'KET_JUMLAH_MIN_SUBJEK' => $get_syarat_f2[$i]->KET_JUMLAH_MIN_SUBJEK,
                                'JUMLAH_MIN_SUBJEK' => $get_syarat_f2[$i]->JUMLAH_MIN_SUBJEK,
                                'SESI' => $get_syarat_f2[$i]->SESI,
                                'ORDERID' => $get_syarat_f2[$i]->ORDERID,
                                'ORDERID2' => $get_syarat_f2[$i]->ORDERID2,
                                'JENIS_SYARAT' => 'SF2',
                                'FASA' => $st_fasa,
                                'UPDATED_USER' => Auth::user()->user_id,
                                'UPDATED_AT' => now()
                            ]);
                    }
                }
                
        }

        // SYARAT FLEKSIBEL 2 SPM ##########################################################

        if (($sf_subjek_1 > 0) && ($sf_subjek_1[0] != '') && ($sf_jum3 != '') && ($sf_gred1_flexi3 != ''))
        { 
            $insert_st = DB::connection('emas')
            ->table('syarat_khas_stpm')
            ->insert(
                [
                    'PROGRAMKOD' => $st_id,
                    'KODSUBJEK' => 'F3'.$st_id,
                    'MINGRED' => $sf_gred1_flexi3,
                    'KUMPULAN' => 'F3',
                    'SUB_KUMPULAN' => 'F3',
                    'JUMLAH_MIN_SUBJEK' => $sf_jum3,
                    'KATEGORI' => 'SPM',
                    'ORDERID' => $sf_susun_3,
                    'SESI' => session()->get('sesi_semasa'),
                    'FASA' => $st_fasa,
                    'UPDATED_AT' => now()
                ]);
            
            for ($i = 0; $i < count($sf_subjek_1); $i++) 
            {
                $insert_st = DB::connection('emas')
                ->table('syarat_xsub_kumpulan_subjek_stpm')
                ->insert(
                    [
                        'KUMPULAN' => 'F3'.$st_id,
                        'KODSUBJEK' => $sf_subjek_1[$i],
                        'SESI' => session()->get('sesi_semasa'),
                        'ORDERID' => $susun_subjekF3[$i],
                    ]); 
            }

                // CHECK AND GET VALUE TO CREATE LOG
                include(app_path() . '/Http/Controllers/es_syarat/stpm/db_view/syaratkhas_f3.php');
                $countf3 = count($get_syarat_f3);

                if ($countf3 != 0)
                {
                    for($i = 0; $i<$countf3; $i++)
                    {
                        $insert_log_syarat = DB::connection('emas')
                        ->table('upu_log_syarat_stpm')
                        ->insert(
                            [
                                'PROGRAMKOD' => $get_syarat_f3[$i]->PROGRAMKOD,
                                'GKUMPULAN' => $get_syarat_f3[$i]->GKUMPULAN,
                                'KODSUBJEK_1' => $get_syarat_f3[$i]->KODSUBJEK_1,
                                'KODSUBJEK_2' => $get_syarat_f3[$i]->KODSUBJEK_2,
                                'MINGRED' => $get_syarat_f3[$i]->MINGRED,
                                'KUMPULAN' => $get_syarat_f3[$i]->KUMPULAN,
                                'SUB_KUMPULAN' => $get_syarat_f3[$i]->SUB_KUMPULAN,
                                'KET_JUMLAH_MIN_SUBJEK' => $get_syarat_f3[$i]->KET_JUMLAH_MIN_SUBJEK,
                                'JUMLAH_MIN_SUBJEK' => $get_syarat_f3[$i]->JUMLAH_MIN_SUBJEK,
                                'SESI' => $get_syarat_f3[$i]->SESI,
                                'ORDERID' => $get_syarat_f3[$i]->ORDERID,
                                'ORDERID2' => $get_syarat_f3[$i]->ORDERID2,
                                'JENIS_SYARAT' => 'SF3',
                                'FASA' => $st_fasa,
                                'UPDATED_USER' => Auth::user()->user_id,
                                'UPDATED_AT' => now()
                            ]);
                    } 
                } 

        }


    // SYARAT KUMPULAN 1 ##############################################################################################################################################################################
     

        if (($sk_subjek_1 > 0) && ($sk_subjek_1[0] != '') && ($sk_jum1 != '') && ($sk_gred1_kump1 != ''))
        {
            $insert_st = DB::connection('emas')
            ->table('syarat_khas_stpm')
            ->insert(
                [
                    'PROGRAMKOD' => $st_id,
                    'KODSUBJEK' => 'G1'.$st_id,
                    'MINGRED' => $sk_gred1_kump1,
                    'KUMPULAN' => 'Y',
                    'SUB_KUMPULAN' => 'N',
                    'JUMLAH_MIN_SUBJEK' => $sk_jum1,
                    'KATEGORI' => $st_kategori,
                    'ORDERID' => $sk_susun_1,
                    'SESI' => session()->get('sesi_semasa'),
                    'FASA' => $st_fasa,
                    'UPDATED_AT' => now()
                ]);

            if ($sf_syarat_flexi1 != 'X1') 
            {

                
                for ($i = 0; $i < count($sk_subjek_1); $i++) 
                {
                    $insert_st = DB::connection('emas')
                    ->table('syarat_sub_kumpulan_subjek_stpm')
                    ->insert(
                        [
                            'KUMPULAN' => 'G1'.$st_id,
                            'KODSUBJEK' => $sk_subjek_1[$i],
                            'SESI' => session()->get('sesi_semasa'),
                            'ORDERID' => $susun_subjekG1[$i],
                        ]);
                }
            }

            // CHECK AND GET VALUE TO CREATE LOG
            include(app_path() . '/Http/Controllers/es_syarat/stpm/db_view/syaratkhas_g1.php');
            $countg1 = count($get_syarat_g1);

            if ($countg1 != 0)
            {
                for($i = 0; $i<$countg1; $i++)
                {
                    $insert_log_syarat = DB::connection('emas')
                    ->table('upu_log_syarat_stpm')
                    ->insert(
                        [
                            'PROGRAMKOD' => $get_syarat_g1[$i]->PROGRAMKOD,
                            'GKUMPULAN' => $get_syarat_g1[$i]->GKUMPULAN,
                            'KODSUBJEK_1' => $get_syarat_g1[$i]->KODSUBJEK_1,
                            'KODSUBJEK_2' => $get_syarat_g1[$i]->KODSUBJEK_2,
                            'MINGRED' => $get_syarat_g1[$i]->MINGRED,
                            'KUMPULAN' => $get_syarat_g1[$i]->KUMPULAN,
                            'SUB_KUMPULAN' => $get_syarat_g1[$i]->SUB_KUMPULAN,
                            'KET_JUMLAH_MIN_SUBJEK' => $get_syarat_g1[$i]->KET_JUMLAH_MIN_SUBJEK,
                            'JUMLAH_MIN_SUBJEK' => $get_syarat_g1[$i]->JUMLAH_MIN_SUBJEK,
                            'SESI' => $get_syarat_g1[$i]->SESI,
                            'ORDERID' => $get_syarat_g1[$i]->ORDERID,
                            'ORDERID2' => $get_syarat_g1[$i]->ORDERID2,
                            'JENIS_SYARAT' => 'SK1',
                            'FASA' => $st_fasa,
                            'UPDATED_USER' => Auth::user()->user_id,
                            'UPDATED_AT' => now()
                        ]);
                }
            }
        }


        if (($sk_subjek_1 > 0) && ($sk_subjek_1[0] != '') && ($sk_jum2 != '') && ($sk_gred2_kump1 != ''))
        {

            $insert_st = DB::connection('emas')
            ->table('syarat_khas_stpm')
            ->insert(
                [
                    'PROGRAMKOD' => $st_id,
                    'KODSUBJEK' => 'GA'.$st_id,
                    'MINGRED' => $sk_gred2_kump1,
                    'KUMPULAN' => 'Y',
                    'SUB_KUMPULAN' => 'N',
                    'JUMLAH_MIN_SUBJEK' => $sk_jum2,
                    'KATEGORI' => $st_kategori,
                    'ORDERID' => $sk_susun_1,
                    'SESI' => session()->get('sesi_semasa'),
                    'FASA' => $st_fasa,
                    'UPDATED_AT' => now()
                ]);

                
            if ($sf_syarat_flexi1 != 'X1') 
            {

                // $count_st = count($sk_subjek_1);
                for ($i = 0; $i <  count($sk_subjek_1); $i++) 
                {
                    $insert_st = DB::connection('emas')
                    ->table('syarat_sub_kumpulan_subjek_stpm')
                    ->insert(
                        [
                            'KUMPULAN' => 'GA'.$st_id,
                            'KODSUBJEK' => $sk_subjek_1[$i],
                            'SESI' => session()->get('sesi_semasa'),
                            'ORDERID' => $susun_subjekG1[$i],
                        ]);
                }
            }


            // CHECK AND GET VALUE TO CREATE LOG
            include(app_path() . '/Http/Controllers/es_syarat/stpm/db_view/syaratkhas_ga.php');
            $countga = count($get_syarat_ga);

            if ($countga != 0)
            {
                for($i = 0; $i<$countga; $i++)
                {
                    $insert_log_syarat = DB::connection('emas')
                    ->table('upu_log_syarat_stpm')
                    ->insert(
                        [
                            'PROGRAMKOD' => $get_syarat_ga[$i]->PROGRAMKOD,
                            'GKUMPULAN' => $get_syarat_ga[$i]->GKUMPULAN,
                            'KODSUBJEK_1' => $get_syarat_ga[$i]->KODSUBJEK_1,
                            'KODSUBJEK_2' => $get_syarat_ga[$i]->KODSUBJEK_2,
                            'MINGRED' => $get_syarat_ga[$i]->MINGRED,
                            'KUMPULAN' => $get_syarat_ga[$i]->KUMPULAN,
                            'SUB_KUMPULAN' => $get_syarat_ga[$i]->SUB_KUMPULAN,
                            'KET_JUMLAH_MIN_SUBJEK' => $get_syarat_ga[$i]->KET_JUMLAH_MIN_SUBJEK,
                            'JUMLAH_MIN_SUBJEK' => $get_syarat_ga[$i]->JUMLAH_MIN_SUBJEK,
                            'SESI' => $get_syarat_ga[$i]->SESI,
                            'ORDERID' => $get_syarat_ga[$i]->ORDERID,
                            'ORDERID2' => $get_syarat_ga[$i]->ORDERID2,
                            'JENIS_SYARAT' => 'SKA',
                            'FASA' => $st_fasa,
                            'UPDATED_USER' => Auth::user()->user_id,
                            'UPDATED_AT' => now()
                        ]);
                }
            }

        }

        // SYARAT KUMPULAN 2 ##########################################################

        // if(count($sk_subjek_2) > 0)
        // {
        //     $delete_st = DB::connection('emas')
        //     ->table('syarat_sub_kumpulan_subjek')
        //     ->where(DB::raw('SUBSTR(KUMPULAN,3,LENGTH(KUMPULAN))'), $st_id)
        //     ->delete();
        // }


        if (($sk_subjek_2 > 0) && ($sk_subjek_2[0] != '') && ($sk_jum3 != '') && ($sk_gred1_kump2 != ''))
        {
            $insert_st = DB::connection('emas')
            ->table('syarat_khas_stpm')
            ->insert(
                [
                    'PROGRAMKOD' => $st_id,
                    'KODSUBJEK' => 'G2'.$st_id,
                    'MINGRED' => $sk_gred1_kump2,
                    'KUMPULAN' => 'Y',
                    'SUB_KUMPULAN' => 'N',
                    'JUMLAH_MIN_SUBJEK' => $sk_jum3,
                    'KATEGORI' => 'SPM',
                    'ORDERID' => $sk_susun_2,
                    'SESI' => session()->get('sesi_semasa'),
                    'FASA' => $st_fasa,
                    'UPDATED_AT' => now()
                ]);

                 
                for ($i = 0; $i < count($sk_subjek_2); $i++) 
                {
                    $insert_st = DB::connection('emas')
                    ->table('syarat_sub_kumpulan_subjek_stpm')
                    ->insert(
                        [
                            'KUMPULAN' => 'G2'.$st_id,
                            'KODSUBJEK' => $sk_subjek_2[$i],
                            'SESI' => session()->get('sesi_semasa'),
                            'ORDERID' => $susun_subjekG2[$i],

                        ]);
                }

                // CHECK AND GET VALUE TO CREATE LOG
                include(app_path() . '/Http/Controllers/es_syarat/stpm/db_view/syaratkhas_g2.php');
                $countg2 = count($get_syarat_g2);

                if ($countg2 != 0)
                {
                    for($i = 0; $i<$countg2; $i++)
                    {
                        $insert_log_syarat = DB::connection('emas')
                        ->table('upu_log_syarat_stpm')
                        ->insert(
                            [
                                'PROGRAMKOD' => $get_syarat_g2[$i]->PROGRAMKOD,
                                'GKUMPULAN' => $get_syarat_g2[$i]->GKUMPULAN,
                                'KODSUBJEK_1' => $get_syarat_g2[$i]->KODSUBJEK_1,
                                'KODSUBJEK_2' => $get_syarat_g2[$i]->KODSUBJEK_2,
                                'MINGRED' => $get_syarat_g2[$i]->MINGRED,
                                'KUMPULAN' => $get_syarat_g2[$i]->KUMPULAN,
                                'SUB_KUMPULAN' => $get_syarat_g2[$i]->SUB_KUMPULAN,
                                'KET_JUMLAH_MIN_SUBJEK' => $get_syarat_g2[$i]->KET_JUMLAH_MIN_SUBJEK,
                                'JUMLAH_MIN_SUBJEK' => $get_syarat_g2[$i]->JUMLAH_MIN_SUBJEK,
                                'SESI' => $get_syarat_g2[$i]->SESI,
                                'ORDERID' => $get_syarat_g2[$i]->ORDERID,
                                'ORDERID2' => $get_syarat_g2[$i]->ORDERID2,
                                'JENIS_SYARAT' => 'SK2',
                                'FASA' => $st_fasa,
                                'UPDATED_USER' => Auth::user()->user_id,
                                'UPDATED_AT' => now()
                            ]);
                    }
                }
        }

        // SYARAT KUMPULAN 3 ##########################################################
        
        if (($sk_subjek_3 > 0) && ($sk_subjek_3[0] != '') && ($sk_jum5 != '') && ($sk_gred1_kump3 != ''))
        {
            $insert_st = DB::connection('emas')
            ->table('syarat_khas_stpm')
            ->insert(
                [
                    'PROGRAMKOD' => $st_id,
                    'KODSUBJEK' => 'G3'.$st_id,
                    'MINGRED' => $sk_gred1_kump3,
                    'KUMPULAN' => 'Y',
                    'SUB_KUMPULAN' => 'N',
                    'JUMLAH_MIN_SUBJEK' => $sk_jum5,
                    'KATEGORI' => 'SPM',
                    'ORDERID' => $sk_susun_3,
                    'SESI' => session()->get('sesi_semasa'),
                    'FASA' => $st_fasa,
                    'UPDATED_AT' => now()
                ]);


            for ($i = 0; $i < count($sk_subjek_3); $i++) 
            {
                $insert_st = DB::connection('emas')
                ->table('syarat_sub_kumpulan_subjek_stpm')
                ->insert(
                    [
                        'KUMPULAN' => 'G3'.$st_id,
                        'KODSUBJEK' => $sk_subjek_3[$i],
                        'SESI' => session()->get('sesi_semasa'),
                        'ORDERID' => $susun_subjekG3[$i],
                    ]);
            }

                // CHECK AND GET VALUE TO CREATE LOG
                include(app_path() . '/Http/Controllers/es_syarat/stpm/db_view/syaratkhas_g3.php');
                $countg3 = count($get_syarat_g3);

                if ($countg3 != 0)
                {
                    for($i = 0; $i<$countg3; $i++)
                    {
                        $insert_log_syarat = DB::connection('emas')
                        ->table('upu_log_syarat_stpm')
                        ->insert(
                            [
                                'PROGRAMKOD' => $get_syarat_g3[$i]->PROGRAMKOD,
                                'GKUMPULAN' => $get_syarat_g3[$i]->GKUMPULAN,
                                'KODSUBJEK_1' => $get_syarat_g3[$i]->KODSUBJEK_1,
                                'KODSUBJEK_2' => $get_syarat_g3[$i]->KODSUBJEK_2,
                                'MINGRED' => $get_syarat_g3[$i]->MINGRED,
                                'KUMPULAN' => $get_syarat_g3[$i]->KUMPULAN,
                                'SUB_KUMPULAN' => $get_syarat_g3[$i]->SUB_KUMPULAN,
                                'KET_JUMLAH_MIN_SUBJEK' => $get_syarat_g3[$i]->KET_JUMLAH_MIN_SUBJEK,
                                'JUMLAH_MIN_SUBJEK' => $get_syarat_g3[$i]->JUMLAH_MIN_SUBJEK,
                                'SESI' => $get_syarat_g3[$i]->SESI,
                                'ORDERID' => $get_syarat_g3[$i]->ORDERID,
                                'ORDERID2' => $get_syarat_g3[$i]->ORDERID2,
                                'JENIS_SYARAT' => 'SK3',
                                'FASA' => $st_fasa,
                                'UPDATED_USER' => Auth::user()->user_id,
                                'UPDATED_AT' => now()
                            ]);
                    }
                }


        }

        $sl_pngk = $request->input('SL_PNGK');
        if($sl_pngk!='') { $pngk='1'; } else { $pngk='0'; }
        
        $sl_tstam = $request->input('SL_TSTAM');
        if($sl_tstam!='') { $tstam='1'; } else { $tstam='0'; }

        $sl_band1 = $request->input('SL_BAND1');
        $sl_band2 = $request->input('SL_BAND2');

        if($sl_band1!='' || $sl_band2!='') { $allband='1'; } else { $allband='0'; }
        
        $sl_tahun1 = $request->input('SL_TAHUN1');
        $sl_tahun2 = $request->input('SL_TAHUN2');

        $sl_bi = $request->input('SL_BI');
        if($sl_bi!='') { $bi_1119='1'; } else { $bi_1119='0'; }

        $sl_kahwin = $request->input('SL_KAHWIN');
        if($sl_kahwin=='B') { $kahwin='1'; } else { $kahwin='0'; }

        $sl_jantina = $request->input('SL_JANTINA');
      
        if($sl_jantina=='L' || $sl_jantina=='P') { $jantina='1'; } else { $jantina='0'; }

        $sl_opumur = $request->input('opumur');
        if($sl_opumur == 'T') { $opumur='0'; } else { $opumur='1'; }

        $sl_umur = $request->input('umur');
        $sl_bulan = $request->input('bulan');

        $total_bulan = (($sl_umur*12) + $sl_bulan);

        $sl_umur1 = $request->input('umur1');
        $sl_bulan1 = $request->input('bulan1');

        $total_bulan1 = (($sl_umur1*12) + $sl_bulan1);

        $sl_3m = $request->input('SL_3M');

        $valid_oku = \DB::connection('emas')->table('program_stpm')->select('OKU')->where('KODPROGRAM', $st_id)->where('LEPASAN','STPM')->where('sesi', session()->get('sesi_semasa'))->get();

        if(!empty($valid_oku[0]) && ($valid_oku[0]->OKU=='T' || $valid_oku[0]->OKU=='N')) {$oku='0';} elseif(!empty($valid_oku[0]) && $valid_oku[0]->OKU=='Y') { $oku='1'; } else { $oku='0'; }
        
        // dd($sl_jantina);

        $count_syarat_lain = \DB::connection('emas')->table('syarat_lain')->where('Programkod', $st_id)->where('Kategori', $st_kategori)->where('sesi', session()->get('sesi_semasa'))->get();
        $valid_syarat_lain = count($count_syarat_lain);
        
// dd($count_syarat_lain);

        if($valid_syarat_lain > 0)
        {
            $update_syarat_lain = DB::connection('emas')
            ->table('syarat_lain')
            ->where('Programkod', $st_id)
            ->where('Kategori', $st_kategori)
            ->where('sesi', session()->get('sesi_semasa'))
            ->update(
                [
                    'syarat_tahap_STAM' => $tstam, 
                    'tahap_STAM' => $sl_tstam, 
                    'Syarat_PNGK_STPM' => $pngk, 
                    'PNGK_STPM' => $sl_pngk, 
                    'Syarat_MUET' => $allband, 
                    'MUET1_BAND' => $sl_band1, 
                    'MUET1_Tahun' => $sl_tahun1, 
                    'MUET2_Band' => $sl_band2, 
                    'MUET2_Tahun' => $sl_tahun2, 
                    'Syarat_1119' => $bi_1119, 
                    'gred_1119' => $sl_bi, 
                    'Syarat_taraf_perkahwinan' => $kahwin, 
                    'Taraf_perkahwinan' => $sl_kahwin, 
                    'Syarat_Jantina' => $jantina, 
                    'Jantina' => $sl_jantina, 
                    'Syarat_Umur' => $opumur, 
                    'Operasi_Umur' => $sl_opumur, 
                    'Umur1' => $sl_umur, 
                    'bulan1' => $sl_bulan, 
                    'umur_bulan1' => $total_bulan, 
                    'Umur2' => $sl_umur1 , 
                    'bulan2' =>  $sl_bulan1, 
                    'umur_bulan2' =>  $total_bulan1, 
                    'Syarat_Kecacatan' =>  $oku, 
                    'syarat_3M' =>  $sl_3m, 
                    'fasa' => $st_fasa,
                    'updated_at' => now()
                ]);
        }
        else
        {
            $insert_syarat_lain = DB::connection('emas')
            ->table('syarat_lain')
            ->insert(
                [
                    'Programkod' => $st_id , 
                    'Kategori' => $st_kategori , 
                    'Syarat_PNGK_STPM' => $pngk, 
                    'PNGK_STPM' => $sl_pngk, 
                    'syarat_tahap_STAM' => $tstam, 
                    'tahap_STAM' => $sl_tstam, 
                    'Syarat_MUET' => $allband, 
                    'MUET1_BAND' => $sl_band1, 
                    'MUET1_Tahun' => $sl_tahun1, 
                    'MUET2_Band' => $sl_band2, 
                    'MUET2_Tahun' => $sl_tahun2, 
                    'Syarat_1119' => $bi_1119, 
                    'gred_1119' => $sl_bi, 
                    'Syarat_taraf_perkahwinan' => $kahwin , 
                    'Taraf_perkahwinan' => $sl_kahwin, 
                    'Syarat_Jantina' => $jantina, 
                    'Jantina' => $sl_jantina, 
                    'Syarat_Umur' => $opumur, 
                    'Operasi_Umur' => $sl_opumur, 
                    'Umur1' => $sl_umur, 
                    'bulan1' => $sl_bulan, 
                    'umur_bulan1' => $total_bulan, 
                    'Umur2' => $sl_umur1 , 
                    'bulan2' =>  $sl_bulan1, 
                    'umur_bulan2' =>  $total_bulan1, 
                    'Syarat_Kecacatan' =>  $oku, 
                    'syarat_3M' =>  $sl_3m, 
                    'sesi' =>  session()->get('sesi_semasa'), 
                    'fasa' => $st_fasa,
                    'updated_at' => now()
                ]);  
        }

        $update_catatan = DB::connection('emas')
        ->table('program_stpm')
        ->where('KODPROGRAM', $st_id)
        ->where('LEPASAN','stpm')
        ->where('sesi', session()->get('sesi_semasa'))
        ->update(
            [
                'REMARKS' => trim($st_catat)
            ]);


		//return redirect()->back()->with('success', 'Syarat Khas bagi Program ' .$st_id. ' telah dikemaskini.');
        return redirect('stpm/esyaratkhas?page=1')->with('success', 'Syarat Khas bagi Program ' .$st_id. ' telah dikemaskini.'); 
        // return back()->with('success', 'Syarat Khas bagi Program' .$st_id. ' telah dikemaskini.');

    }

    public function salin(Request $request)
    {
        // PERLU TUKAR MENGIKUT KESESUAIAN UNTUK KEMASKINI
        // FLAG UNTUK FASA KEMASKINI
        // 1 = FASA TAFSIRAN 2 = BUKAN FASA TAFSIRAN
        $fasa_kemaskini = '1';

        // REQUEST HIDDEN FORM
        $st_fasa = $fasa_kemaskini;
        $id = $request->input('id');
        $kodprogram = $request->input('kodprogram');
        $kategori = $request->input('kategori');
        $salin_kod1 = $request->input('SALIN_SYARAT1');
        $salin_kod2 = $request->input('SALIN_SYARAT2');
        $salin_kod3 = $request->input('SALIN_SYARAT3');
        // $salin_kod4 = $request->input('SALIN_SYARAT4');
        // $salin_kod5 = $request->input('SALIN_SYARAT5');

        $sesi_semasa = session()->get('sesi_semasa');

        if($salin_kod1!='') { include(app_path() . '/Http/Controllers/es_syarat/stpm/pakej_salin/salin_1.php'); }  
        if($salin_kod2!='') { include(app_path() . '/Http/Controllers/es_syarat/stpm/pakej_salin/salin_2.php'); }  
        if($salin_kod3!='') { include(app_path() . '/Http/Controllers/es_syarat/stpm/pakej_salin/salin_3.php'); }  
        // if($salin_kod4!='') { include(app_path() . '/Http/Controllers/es_syarat/stpm/pakej_salin/salin_4.php'); }  
        // if($salin_kod5!='') { include(app_path() . '/Http/Controllers/es_syarat/stpm/pakej_salin/salin_5.php'); }  
		
		if($salin_kod1!='') { $kodprogram_salin = [$salin_kod1];}
		if($salin_kod2!='') { $kodprogram_salin = [$salin_kod2];}
		if($salin_kod3!='') { $kodprogram_salin = [$salin_kod3];}
		if($salin_kod1!='' && $salin_kod2!='') { $kodprogram_salin = [$salin_kod1,$salin_kod2];}
		if($salin_kod1!='' && $salin_kod3!='') { $kodprogram_salin = [$salin_kod1,$salin_kod3];}
		if($salin_kod2!='' && $salin_kod3!='') { $kodprogram_salin = [$salin_kod2,$salin_kod3];}
		if($salin_kod1!='' && $salin_kod2!=''  && $salin_kod3!='') { $kodprogram_salin = [$salin_kod1,$salin_kod2,$salin_kod3];}
		
		$message=join(' , ',$kodprogram_salin);
        return redirect()->back()->with('success', 'Syarat bagi program ' .$id. ' telah berjaya disalin ke ' .$message );
    }

    public function salin2(Request $request)
    {
        // PERLU TUKAR MENGIKUT KESESUAIAN UNTUK KEMASKINI
        // FLAG UNTUK FASA KEMASKINI
        // 1 = FASA TAFSIRAN 2 = BUKAN FASA TAFSIRAN
        $fasa_kemaskini = '1';

        // REQUEST HIDDEN FORM
        $st_fasa = $fasa_kemaskini;
        $id = $request->input('id');
        $kodprogram = $request->input('kodprogram');
        $kategori = $request->input('kategori');
        $jensetaraf = $request->input('jensetaraf');
        
        $salin_kod1 = $request->input('SALIN_SYARAT1');
        $salin_kod2 = $request->input('SALIN_SYARAT2');
        $salin_kod3 = $request->input('SALIN_SYARAT3');
        //$salin_kod4 = $request->input('SALIN_SYARAT4');
        //$salin_kod5 = $request->input('SALIN_SYARAT5');

        $sesi_semasa = session()->get('sesi_semasa');

        if($salin_kod1!='') { include(app_path() . '/Http/Controllers/es_syarat/stpm/pakej_salin_diploma/salin_1.php'); }  
        if($salin_kod2!='') { include(app_path() . '/Http/Controllers/es_syarat/stpm/pakej_salin_diploma/salin_2.php'); }  
        if($salin_kod3!='') { include(app_path() . '/Http/Controllers/es_syarat/stpm/pakej_salin_diploma/salin_3.php'); }  
        // if($salin_kod4!='') { include(app_path() . '/Http/Controllers/es_syarat/stpm/pakej_salin_diploma/salin_4.php'); }  
        // if($salin_kod5!='') { include(app_path() . '/Http/Controllers/es_syarat/stpm/pakej_salin_diploma/salin_5.php'); }  

        return redirect()->back()->with('success', 'Syarat bagi program ' .$id. ' telah berjaya disalin ke ' .$salin_kod1 );
    }

    public function prnpreview()
    {
        // ini_set('max_execution_time', '300');
        $codeset_oku = DB::connection('upucodeset')->table('refperibadi_jencacat')->get();
        $skgred = DB::connection('upucodeset')->table('refspm_gred')->whereNotIn('kodspmgred',['R','T','X','Z'])->get();
        $sksubjek = DB::connection('upucodeset')->table('refspm_subjek')->whereNotIn('kodsubjekspm',['0000'])->get();
        $codeset_muet = DB::connection('upucodeset')->table('refmuet_thp')->get();
        $codeset_muet2 = DB::connection('upucodeset')->table('refmuet_thp_2')->get();
		$codeset_tstam = DB::connection('upucodeset')->table('refstam_thp')->get();
		
        if(session()->get('login_jenprog')=='stpm')
        {
         if(session()->get('kategori') =='stpm') { $kat= ['A','S']; } 
         elseif(session()->get('kategori') =='matrik') { $kat= ['P','L','U','N','K','J','M','V']; } 
         elseif(session()->get('kategori') =='stam') { $kat= ['T']; } 
         elseif(session()->get('kategori') =='dipG') { $kat= ['G']; } 
         elseif(session()->get('kategori') =='dipE') { $kat= ['E']; } 
         elseif(session()->get('kategori') =='dipF') { $kat= ['F']; } 
         else { $kat= ['A','S']; }
        //  else { $kat= ['A','S','P','L','U','N','K','J','M','V','T','G','E','F']; } 
        }
    
        $sessionSesi = session()->get('sesi_semasa');
        include(app_path() . '/Http/Controllers/es_syarat/stpm/include_cetak_syarat/upuplus_program_stpm.php');
        
        $sessionIpta = session()->get('ipta');
        if (in_array(Auth::user()->ipta, ['11', '22'])) {
            $iptaKod = $sessionIpta != '' ? $sessionIpta : 'UA';
        } else {
            $iptaKod = Auth::user()->ipta;
        }

        $codeset_ipta = DB::connection('emas')->table('upu_ipta2')->where('IPTA_KOD', $iptaKod)->first();
        return view('es_syaratkhas.stpm.cetak', compact('cetak','codeset_ipta','codeset_oku','codeset_muet','codeset_muet2','codeset_tstam'));
    }

    public function cetak_oku()
    {
        $codeset_oku = DB::connection('upucodeset')->table('refperibadi_jencacat')->get();

        $cetak_oku = DB::connection('emas')->table('program_stpm')
        ->where('LEPASAN',session()->get('login_jenprog'))
        ->where('sesi',session()->get('sesi_semasa'))
        ->where('STATUS_TAWAR','Y')
        ->when(in_array(Auth::user()->ipta, ['11','22']) && session()->get('ipta') != '', function ($q) {
            return $q->where(DB::raw('substr(KODPROGRAM_PAPAR, 1, 2)'), 'like', '%'.session()->get('ipta').'%')
                    ->where('KODPROGRAM_PAPAR', 'like', '%'.session()->get('program').'%');
        })
        ->when(in_array(Auth::user()->ipta, ['11','22']) && session()->get('ipta') == '', function ($q) {
            return $q->where(DB::raw('substr(KODPROGRAM_PAPAR, 1, 2)'), 'UA')
                    ->where('KODPROGRAM_PAPAR', 'like', '%'.session()->get('program').'%');
        }) 
        ->when(!in_array(Auth::user()->ipta, ['11','22']), function ($q) {
            return $q->where(DB::raw('substr(KODPROGRAM_PAPAR, 1, 2)'), Auth::user()->ipta)
                    ->where('KODPROGRAM_PAPAR', 'like', '%'.session()->get('program').'%');
        })
        ->groupby('KODPROGRAM_PAPAR')
        ->orderby('KODPROGRAM_PAPAR','ASC')
        ->get();


        include(app_path() . '/Http/Controllers/es_syarat/stpm/include_cetak_syarat/upuplus_jenis_oku.php');
       
        $list_oku = $upuplus_jenis_oku;        
        

        $sessionIpta = session()->get('ipta');
        if (in_array(Auth::user()->ipta, ['11', '22'])) {
            $iptaKod = $sessionIpta != '' ? $sessionIpta : 'UA';
        } else {
            $iptaKod = Auth::user()->ipta;
        }
        
        $codeset_ipta = DB::connection('emas')->table('upu_ipta2')->where('IPTA_KOD', $iptaKod)->first();

        $pdf = PDF::loadView('es_syaratkhas.stpm.cetak_oku',['cetak_oku' => $cetak_oku, 'list_oku' => $list_oku, 'codeset_ipta' => $codeset_ipta, 'codeset_oku' => $codeset_oku]);  
        return $pdf->stream('SENARAI PROGRAM VS ORANG KURANG UPAYA (OKU).pdf');
    }

    public function modal_salin(Request $request,$idsyarat)
    {
        $modal_syarat = DB::connection('emas')->table('program_stpm')
        ->where('sesi',session()->get('sesi_semasa'))
        ->where('KODPROGRAM', $idsyarat)
        ->where('LEPASAN', session()->get('login_jenprog'))
        ->get(); 
		

        $modal_katag = DB::connection('emas')->table('program_stpm')
        ->where('sesi',session()->get('sesi_semasa'))
        ->where('LEPASAN', session()->get('login_jenprog'))
        ->get();  

        $kod_program = DB::connection('emas')->table('program_stpm')
        ->where('LEPASAN',session()->get('login_jenprog'))
        ->where('sesi',session()->get('sesi_semasa'))
        ->where('STATUS_TAWAR','Y')
        ->when(in_array(Auth::user()->ipta, ['11','22']) && session()->get('ipta') != '', function ($q) {
            return $q->where(DB::raw('substr(KODPROGRAM_PAPAR, 1, 2)'), 'like', '%'.session()->get('ipta').'%');
        })
        ->when(in_array(Auth::user()->ipta, ['11','22']) && session()->get('ipta') == '', function ($q) {
            return $q->where(DB::raw('substr(KODPROGRAM_PAPAR, 1, 2)'), 'UA');
        }) 
        ->when(!in_array(Auth::user()->ipta, ['11','22']), function ($q) {
            return $q->where(DB::raw('substr(KODPROGRAM_PAPAR, 1, 2)'), Auth::user()->ipta);
        })
        ->orderby('KODPROGRAM','ASC')
        ->get();

		return view('es_syaratkhas.stpm.salin',compact('modal_syarat','modal_katag','kod_program'));
    }


    public function modal_salin_diploma(Request $request,$idsyarat, $idjensetaraf)
    {
        $modal_syarat = DB::connection('emas')->table('program_stpm')
        ->where('sesi',session()->get('sesi_semasa'))
        ->where('KODPROGRAM', $idsyarat)
        ->where('JENSETARAF', $idjensetaraf)
        ->where('LEPASAN', session()->get('login_jenprog'))
        ->get();  

        $modal_katag = DB::connection('emas')->table('program_stpm')
        ->where('sesi',session()->get('sesi_semasa'))
        ->where('LEPASAN', session()->get('login_jenprog'))
        ->get();  

        $kod_program = DB::connection('emas')->table('program_stpm')
        ->where('LEPASAN',session()->get('login_jenprog'))
        ->where('sesi',session()->get('sesi_semasa'))
        ->where('STATUS_TAWAR','Y')
        ->where('JENSETARAF', $idjensetaraf)
        ->when(in_array(Auth::user()->ipta, ['11','22']) && session()->get('ipta') != '', function ($q) {
            return $q->where(DB::raw('substr(KODPROGRAM_PAPAR, 1, 2)'), 'like', '%'.session()->get('ipta').'%');
        })
        ->when(in_array(Auth::user()->ipta, ['11','22']) && session()->get('ipta') == '', function ($q) {
            return $q->where(DB::raw('substr(KODPROGRAM_PAPAR, 1, 2)'), 'UA');
        }) 
        ->when(!in_array(Auth::user()->ipta, ['11','22']), function ($q) {
            return $q->where(DB::raw('substr(KODPROGRAM_PAPAR, 1, 2)'), Auth::user()->ipta);
        })
        ->orderby('KODPROGRAM','ASC')
        ->get();

        return view('es_syaratkhas.stpm.salin_diploma',compact('modal_syarat','modal_katag','kod_program'));
    }

    public function cetak_syarat(Request $request)
    {
        $sessionSesi = session()->get('sesi_semasa');
        include(app_path() . '/Http/Controllers/es_syarat/stpm/include_cetak_syarat/upuplus_program_stpm.php');

        if(session()->get('kategori') =='stpm') { $kat= ['A','S']; } 
        elseif(session()->get('kategori') =='matrik') { $kat= ['P','L','U','N','K','J','M','V']; } 
        elseif(session()->get('kategori') =='stam') { $kat= ['T']; } 
        elseif(session()->get('kategori') =='dipG') { $kat= ['G']; } 
        elseif(session()->get('kategori') =='dipE') { $kat= ['E']; } 
        elseif(session()->get('kategori') =='dipF') { $kat= ['F']; } 
        else { $kat= ['A','S']; } 
        // else { $kat= ['A','S','P','L','U','N','K','J','M','V','T','G','E','F']; } 

        $cetak_syarat = DB::connection('emas')->table('upuplus_program_stpm')
        ->where('lepasan',session()->get('login_jenprog'))
        ->where(DB::raw('substr(kodprogram, 1, 2)'), 'like', session()->get('ipta').'%')
        ->where(function($query) {
            $query->orWhere('kodprogram', 'like', '%'.session()->get('program').'%');
            }) 
        ->when(in_array(Auth::user()->ipta, ['11','22']) && session()->get('ipta') != '', function ($q) {
            return $q->where(DB::raw('substr(kodprogram, 1, 2)'), 'like', '%'.session()->get('ipta').'%');
        })
        ->when(in_array(Auth::user()->ipta, ['11','22']) && session()->get('ipta') == '', function ($q) {
            return $q->where(DB::raw('substr(kodprogram, 1, 2)'), 'UA');
        })
        ->when(!in_array(Auth::user()->ipta, ['11','22']), function ($q) {
            return $q->where(DB::raw('substr(kodprogram, 1, 2)'), Auth::user()->ipta);
        })
        ->whereIn('kategori', $kat)
        ->where('status_tawar','Y')
        ->orderby('kodprogram','ASC')
        ->orderby('kategori','ASC')
        ->groupby('kodprogram')
        ->groupby('kategori')
        ->groupby('jensetaraf')
        ->get(); 

        $iptaKod = session()->get('ipta') ?: (Auth::user()->ipta == '11' || Auth::user()->ipta == '22' ? 'UA' : Auth::user()->ipta);
        $codeset_ipta3 = DB::connection('emas')->table('upu_ipta2')->where('IPTA_KOD', $iptaKod)->first();

        $codeset_ipta = DB::connection('upucodeset')->table('refemas_institusi')->whereNotIn('kodipta',['1','2'])->orderBy('sorting', 'ASC')->get();
        $codeset_peringkat = DB::connection('upucodeset')->table('refemas_peringkat')->get();
        $codeset_bidang_nec = DB::connection('upucodeset')->table('refalldip_bidangnec')->get();
        $codeset_oku = DB::connection('upucodeset')->table('refperibadi_jencacat')->get();
        $skgred = DB::connection('upucodeset')->table('refspm_gred')->whereNotIn('kodspmgred',['R','T','X','Z'])->get();
        $sksubjek = DB::connection('upucodeset')->table('refspm_subjek')->whereNotIn('kodsubjekspm',['0000'])->get();
        $codeset_muet2 = DB::connection('upucodeset')->table('refmuet_thp_2')->get();
        $codeset_muet = DB::connection('upucodeset')->table('refmuet_thp')->get();
        $codeset_tstam = DB::connection('upucodeset')->table('refstam_thp')->get();

        $pdf = PDF::loadView('es_syaratkhas.stpm.cetak',['cetak_syarat' => $cetak_syarat,'codeset_ipta3' => $codeset_ipta3,'codeset_ipta' => $codeset_ipta,
        'codeset_peringkat' => $codeset_peringkat,'codeset_bidang_nec' => $codeset_bidang_nec,'codeset_oku' => $codeset_oku,'skgred' => $skgred,
        'sksubjek' => $sksubjek,'codeset_muet2' => $codeset_muet2,'codeset_muet' => $codeset_muet,'codeset_tstam' => $codeset_tstam]); 
        return $pdf->stream('SYARAT_KHAS.pdf');
    }
}
