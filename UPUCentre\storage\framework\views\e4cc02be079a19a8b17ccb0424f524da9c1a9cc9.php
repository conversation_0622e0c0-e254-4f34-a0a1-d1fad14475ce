<?php $__env->startSection('content'); ?>

<style>
    .th {font-weight: normal;}
    .lbl_font {font-weight: bold; font-size: 0.85rem;}
    .tbl_align{ text-align: center; vertical-align: middle}
    .lbl_text { color: #000; }

    .fade2 {
        transform: scale(0);
        opacity: 0;
        -webkit-transition: all .6s linear;/*opening speed*/
        -o-transition: all .6s linear;/*opening speed*/
        transition: all .6s linear;/*opening speed*/
    }   
    .fade2.show { opacity: 1;transform: scale(1); }
    .container-layout{ position:relative; top:28vh; }
    .modal-layout{ height: 80vh; overflow-y: auto; }

    .has-error .form-control { border-color: #a94442; }
    
    /* Tab styling */
    .nav-tabs .nav-link {
        border: 1px solid #dee2e6;
        border-bottom: none;
        background-color: #f8f9fa;
        color: #495057;
    }
    .nav-tabs .nav-link.active {
        background-color: #fff;
        border-color: #dee2e6 #dee2e6 #fff;
        color: #007bff;
        font-weight: bold;
    }
    .tab-content {
        border: 1px solid #dee2e6;
        border-top: none;
        padding: 20px;
        background-color: #fff;
    }
</style>

<div class="container-fluid">
    <!-- Tab Navigation -->
    <ul class="nav nav-tabs" id="upuAdminTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <a class="nav-link active" id="rekod-mohon-tab" data-toggle="tab" href="#rekod-mohon" role="tab" aria-controls="rekod-mohon" aria-selected="true">
                <i class="fas fa-search"></i> Rekod Mohon
            </a>
        </li>
        <li class="nav-item" role="presentation">
            <a class="nav-link" id="semakan-pelajar-tab" data-toggle="tab" href="#semakan-pelajar" role="tab" aria-controls="semakan-pelajar" aria-selected="false">
                <i class="fas fa-user-graduate"></i> Semakan Pelajar
            </a>
        </li>
    </ul>

    <!-- Tab Content -->
    <div class="tab-content" id="upuAdminTabContent">
        <!-- Tab 1: Rekod Mohon -->
        <div class="tab-pane fade show active" id="rekod-mohon" role="tabpanel" aria-labelledby="rekod-mohon-tab">
            <div class="row">
                <div class="col-xl-4 col-lg-4 mb-3">

            <div class="border rounded p-4 form-group col-xl-12">
                <?php echo Form::open(['method' => 'POST', 'route' => 'upuadmin.cari', 'class' => 'form-horizontal']); ?>

            
                <div class="form-group<?php echo e($errors->has('NOKP') ? ' has-error' : ''); ?> mt-n2">
                    <?php echo Form::label('NOKP', 'NO KAD PENGENALAN', ['class' => 'lbl_font mb-n2']); ?>

                    <?php echo Form::text('NOKP', old('NOKP',request()->input('NOKP')), ['class' => 'form-control form-control-sm', 'maxlength' => 12,'style'=>'text-transform: uppercase;']); ?>

                    <small class="text-danger"><?php echo e($errors->first('NOKP')); ?></small>
                </div>
        
                <p class="lbl_font">PROGRAM PENGAJIAN : </p>
                <div class="form-group mt-n2">            
                    <div class="radio<?php echo e($errors->has('JENPROG') ? ' has-error' : ''); ?> mt-n2">
                        <div class="custom-control custom-radio custom-control-inline">
                            <input type="radio" id="JENPROG1" name="JENPROG" value="spm" class="custom-control-input" checked='checked' onclick="jenprog1();" <?php if(old('JENPROG',request()->input('JENPROG')) == "spm"): ?> <?php echo e('checked'); ?> <?php endif; ?>>
                            <label class="custom-control-label" for="JENPROG1">Lepasan SPM</label>
                        </div>                    
                        <div class="custom-control custom-radio custom-control-inline">
                            <input type="radio" id="JENPROG2" name="JENPROG" value="stpm" class="custom-control-input"  onclick="jenprog2();" <?php if(old('JENPROG',request()->input('JENPROG')) == "stpm"): ?> <?php echo e('checked'); ?> <?php endif; ?>>
                            <label class="custom-control-label" for="JENPROG2">Lepasan STPM / Setaraf</label>
                        </div>
                    </div>     
                </div>
                
            <p class="lbl_font">JENIS SEMAKAN: <span class="text-danger font-weight-normal"><?php echo e($errors->first('SEMAKAN')); ?></span></p>
                <div class="form-group mt-n2">
                   <div class="radio<?php echo e($errors->has('SEMAKAN') ? ' has-error' : ''); ?> mt-n2"">
                         <div class="custom-control custom-radio">
                             <input type="radio" id="SEMAKAN1" name="SEMAKAN" value="fmohon" class="custom-control-input" <?php if(old('SEMAKAN',request()->input('SEMAKAN')) == "fmohon"): ?> <?php echo e('checked'); ?> <?php endif; ?>>
                             <label class="custom-control-label" for="SEMAKAN1">Fasa Permohonan</label>
                         </div>        
                         <div class="custom-control custom-radio">
                             <input type="radio" id="SEMAKAN2" name="SEMAKAN" value="fkemaskini" class="custom-control-input" <?php if(old('SEMAKAN',request()->input('SEMAKAN')) == "fkemaskini"): ?> <?php echo e('checked'); ?> <?php endif; ?>>
                             <label class="custom-control-label" for="SEMAKAN2">Fasa Kemaskini</label>
                         </div>
                         <div class="custom-control custom-radio">
                             <input type="radio" id="SEMAKAN3" name="SEMAKAN" value="frayuan" class="custom-control-input" <?php if(old('SEMAKAN',request()->input('SEMAKAN')) == "frayuan"): ?> <?php echo e('checked'); ?> <?php endif; ?>>
                             <label class="custom-control-label" for="SEMAKAN3">Fasa Rayuan</label>
                         </div>
                     </div>   
                </div>

                <div class="mt-2" style="text-align: right;">
                    <?php echo e(Form::button('Cari ', ['type' => 'submit', 'class' => 'btn btn-primary btn-sm col-xl-3 col-lg-4 col-md-3 mb-1', 'id'=>'upuadmin_carian', 'name'=>'upuadmin_carian', 'style'=>'width:60px;'] )); ?>

                    <?php echo e(Form::button('Reset', ['type' => 'submit', 'class' => 'btn btn-warning btn-sm col-xl-3 col-lg-4 col-md-3 mb-1', 'id'=>'upuadmin_reset', 'name'=>'upuadmin_reset', 'style'=>'width:60px;'] )); ?>

                </div>             
                <?php echo Form::close(); ?>

            </div>
        </div>

        <?php if(request()->input('SEMAKAN')=='fmohon'): ?>
            <?php echo $__env->make('mc_rekod_mohon.index_mohon', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?> 
        <?php elseif(request()->input('SEMAKAN')=='fkemaskini'): ?>
            <?php echo $__env->make('mc_rekod_mohon.index_kemaskini', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php elseif(request()->input('SEMAKAN')=='frayuan'): ?>
            <?php echo $__env->make('mc_rekod_mohon.index_rayuan', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php endif; ?>

            </div>
        </div>
        
        <!-- Tab 2: Semakan Pelajar -->
        <div class="tab-pane fade" id="semakan-pelajar" role="tabpanel" aria-labelledby="semakan-pelajar-tab">
            <?php echo $__env->make('mc_rekod_mohon.semakan_pelajar_content', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        </div>
    </div>
</div>


<div class="modal fade2" id="error" tabindex="-1" role="dialog" aria-labelledby="errorLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content" style="border-radius: .4rem;">

            <?php if(count($errors)): ?>
                <div class="modal-header bg-danger p-1"></div>
                <div class="modal-body text-center">
                <span aria-hidden="true" class="close" data-dismiss="modal" aria-label="Close" style="margin-top: -13px; margin-right: -15px; font-size: 1.2rem;">
                    <i class="fas fa-fw fa-times-circle"></i>
                </span>
                    <div style="font-size: 40px;" class="text-danger mt-4"><i class="far fa-fw fa-times-circle"></i>RALAT</div>
                    <div class="mt-3">Maklumat yang anda masukkan tidak lengkap.</div>
                    <small>Sila semak semula input anda dan cuba lagi!</small>
                <div class="mt-4 mb-3">
                <button type="button" class="btn btn-danger btn-sm" data-dismiss="modal"><i class="fa fa-fw fa-close"></i> Semula</button>
                </div>
                </div>
            <?php endif; ?>

            <?php if(session()->has('message')): ?>
                <div class="modal-header bg-warning p-1"></div>
                <div class="modal-body text-center">
                <span aria-hidden="true" class="close" data-dismiss="modal" aria-label="Close" style="margin-top: -13px; margin-right: -15px; font-size: 1.2rem;">
                    <i class="fas fa-fw fa-times-circle"></i>
                </span>
                    <div style="font-size: 40px;" class="text-warning mt-4"><i class="fas fa-fw fa-exclamation-circle"></i>RALAT</div>
                    <div class="mt-3">Tiada rekod permohonan.</div>
                    <small>Sila semak semula input anda dan cuba lagi!</small>
                <div class="mt-4 mb-3">
                <button type="button" class="btn btn-warning btn-sm" data-dismiss="modal"><i class="fa fa-fw fa-close"></i> Semula</button>
                </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>



<div class="modal fade2" id="upu_<?php echo e(request()->input('NOKP')); ?>" tabindex="-1" role="dialog" aria-labelledby="finLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header text-white" style="background-color: #3490dc;">
                <h5 class="modal-title">Maklumat Permohonan</h5>
                <span aria-hidden="true" class="close" data-dismiss="modal" aria-label="Close">
                    <i class="fas fa-times-circle"></i>
                </span>
            </div>
           
            <div class="modal-body modal-layout">
                    <?php if(!empty($mohon) && $mohon->count() > 0): ?>

                        <?php if(request()->input('SEMAKAN')=='fmohon'): ?>
                            <?php echo $__env->make('mc_rekod_mohon.show_mohon', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                        <?php elseif(request()->input('SEMAKAN')=='fkemaskini'): ?>
                            <?php echo $__env->make('mc_rekod_mohon.show_kemaskini', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                        <?php elseif(request()->input('SEMAKAN')=='frayuan'): ?>
                            <?php echo $__env->make('mc_rekod_mohon.show_rayuan', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                        <?php endif; ?>
                <?php endif; ?>
            </div>

            <div class="modal-footer">
                
            </div>
               
        </div>
    </div>
</div>






<?php if(count($errors) || session()->has('message')): ?>
    <script type="text/javascript">
        $( document ).ready(function() {
            $('#error').modal('show');
        });
    </script>
<?php endif; ?>

<script>
    $(document).ready(function(){
		$('#NOKP').attr('disabled', false);
		$('#JENPROG1').attr('disabled', false);
		$('#JENPROG2').attr('disabled', false);
		// document.getElementById("SEMAKAN1").checked = true;
		document.getElementById("SEMAKAN3").checked = true;
		document.getElementById("JENPROG1").checked = true;
		// document.getElementById("JENPROG2").checked = true;
		
        if($("#JENPROG1").is(":checked"))
        {
	    // document.getElementById("SEMAKAN3").checked = true;
	    $('#SEMAKAN1').attr('disabled', false);
            $('#SEMAKAN2').attr('disabled', false);
            $('#SEMAKAN3').attr('disabled', false);
        }

        if($("#JENPROG2").is(":checked"))
        {
	    document.getElementById("SEMAKAN3").checked = true;
	    $('#SEMAKAN1').attr('disabled', false);
            $('#SEMAKAN2').attr('disabled', false);
            $('#SEMAKAN3').attr('disabled', false);
        }
    });

    function jenprog1() {
        if($("#JENPROG1").is(":checked"))
        {
            document.getElementById("SEMAKAN1").checked = false;
            document.getElementById("SEMAKAN2").checked = false;
            document.getElementById("SEMAKAN3").checked = true;
            $('#SEMAKAN2').attr('disabled', false);
            $('#SEMAKAN3').attr('disabled', false);
            
        }
    }

    function jenprog2() {
        if($("#JENPROG2").is(":checked"))
        {
            document.getElementById("SEMAKAN1").checked = false;
            document.getElementById("SEMAKAN2").checked = false;
            document.getElementById("SEMAKAN3").checked = true;
	    $('#SEMAKAN1').attr('disabled', false);
            $('#SEMAKAN2').attr('disabled', false);
            $('#SEMAKAN3').attr('disabled', false);
        }
    }

    // Auto-activate Semakan Pelajar tab if coming from form submission
    <?php if(session('active_tab') === 'semakan-pelajar'): ?>
        $(document).ready(function() {
            $('#semakan-pelajar-tab').tab('show');
        });
    <?php endif; ?>
    
    // Activate Semakan Pelajar tab if URL indicates it (hash or ?tab=)
    $(document).ready(function() {
        var hash = window.location.hash;
        var params = new URLSearchParams(window.location.search);
        if (hash === '#semakan-pelajar' || params.get('tab') === 'semakan-pelajar') {
            $('#semakan-pelajar-tab').tab('show');
        }
    });

    // Also handle hashchange after page load
    window.addEventListener('hashchange', function() {
        if (window.location.hash === '#semakan-pelajar') {
            $('#semakan-pelajar-tab').tab('show');
        }
    });
</script>

<?php $__env->stopSection(); ?>


<?php echo $__env->make('main.index', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\UPUCentre\resources\views/mc_rekod_mohon/index.blade.php ENDPATH**/ ?>