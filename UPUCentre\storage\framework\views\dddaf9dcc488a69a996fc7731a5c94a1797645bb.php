<style>
    .lbl_font {font-weight: bold; font-size: 0.9rem;}
    .tbl_font {font-size: 0.9rem; }
    .tbl_align{ text-align: center; }
    .lbl_text { color: #000; }
    /* .table td, .table th { vertical-align: middle; } */
    .hoverTable{ border-collapse:collapse; }
    .hoverTable tbody tr:hover {background-color: rgba(0, 0, 0, 0.075);}

    .fade2 {
        transform: scale(0);
        opacity: 0;
        -webkit-transition: all .6s linear;/*opening speed*/
        -o-transition: all .6s linear;/*opening speed*/
        transition: all .6s linear;/*opening speed*/
    }

    .modal-body1{
        height: 80vh;
        overflow-y: auto;
    }

    .fade2.show { opacity: 1;transform: scale(1); }

    .select2 { width: 100% !important; }
    .select2-container .select2-selection--single 
    {
        height: calc(1.5em + .5rem + 2px);
        padding: 1px;
        font-size: .875rem;
        border: 1px solid #d1d3e2;
    }

    .select2-container--default .select2-results>.select2-results__options{ max-height: 400px; font-size: .875rem; } 
    .select2-container--default .select2-selection--single .select2-selection__rendered {line-height: 28px;}
    .select2-container--default .select2-selection--single .select2-selection__rendered  { color: black; }
    .select2-container--default .select2-selection--single .select2-selection__arrow { height: 30px; }
     span.select2-selection--multiple[aria-expanded=true] { border-color: blue !important; }

	.select2-container--default .select2-results__option[aria-disabled=true] { display: none;} 

    /* .select2-container--default .select2-results>.select2-results__options{ max-height: 400px; font-size: .875rem; } 
    .select2-container--default .select2-selection--single .select2-selection__rendered {line-height: 28px; color: black;} */
    /* .select2-container--default .select2-selection--single .select2-selection__rendered  { color: black; } */
    /* .select2-container--default .select2-selection--single .select2-selection__arrow { height: 30px; } */
    /* .dropdown-menu { color: #000; } */
    

    /* span.select2-selection--multiple[aria-expanded=true] { border-color: blue !important; } */

    /* MAKE AUTOMATC LAYOUT BASED ON NAME PROGRAM LIST */
    /* .select2-container .select2-selection--multiple .select2-selection__rendered {white-space: break-spaces;}
    .select2-container .select2-selection--multiple { height: 100%; } */
    /* ############################################### */
    
	
    /* CHANGE LAYOUT BUTTON FOR LIST PROGRAM */
    /* .select2-container--default .select2-selection--single .select2-selection__arrow {
        background-image: -khtml-gradient(linear, left top, left bottom, from(#424242), to(#030303));
        background-image: -moz-linear-gradient(top, #424242, #030303);
        background-image: -ms-linear-gradient(top, #424242, #030303);
        background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #424242), color-stop(100%, #030303));
        background-image: -webkit-linear-gradient(top, #424242, #030303);
        background-image: -o-linear-gradient(top, #424242, #030303);
        background-image: linear-gradient(#424242, #030303);
        width: 40px;
        color: #fff;
        font-size: 1.3em;
        padding: 4px 12px;
        height: 100%;
        position: absolute;
        top: 0px;
        right: 0px;
        width: 20px;
    } */

    .toast {
        border:none;
    }

    .toast{
        border-radius: 13px;
        box-shadow: 1px 7px 14px -5px rgba(0,0,0,0.15);
        width: 430px;
        max-width: 430px;
        align-items: center;
    }

    .toast_success {
        border-left: 5px solid #155724;
    }

    .toast_danger {
        border-left: 5px solid #721c24;
    }

    .toast_warning {
        border-left: 5px solid #856404;
    }

    .alert {
        position: relative;
        padding: 0.75rem 1.25rem;
        margin-bottom: 1rem;
        border: 1px solid transparent;
        border-radius: 10px;
    }

    .pagination {
        justify-content: flex-end!important;
    }

    .tag {
    font-size: 1em;
    padding: .3em .4em .4em;
    margin: 0 .1em;
    }
    .tag a {
    color: #bbb;
    cursor: pointer;
    opacity: 0.6;
    }
    .tag a:hover {
    opacity: 1.0
    }
    .tag .remove {
    top: 0;
    }

    .sbclose {
        float: none;
        font-size: 1.2rem;
        font-weight: 700;
        line-height: 1;
        color: #000;
        text-shadow: 0 1px 0 #fff;
        opacity:unset;
        margin-top: 5px;
    }
  
    .loading-spinner {
        display: none;
    }

    .loading-spinner.active {
        display: inline-block; // or whatever is appropriate to display it nicely
    }

    .loading-copy.inactive {
        display:none;
    }

    .loading-save.inactive {
        display:none;
    }

    .nav-pills .nav-link.active, .nav-pills .show > .nav-link {
        color: #fff;
        background-color: #28a745;
    }

    .nav-pills .nav-link {
        border-radius: 0;
    }
	
    .bg-blue-header
    {
        background-color: #435d7d;
        color: #FFF;
    }

    .btn-mod
    {
        border-radius: 100px;
        padding: 6px 8px 6px 8px;
    }
</style><?php /**PATH C:\xampp\htdocs\UPUCentre\resources\views/es_syaratkhas/spm/custom_css.blade.php ENDPATH**/ ?>