 # SYARAT Popup Modal Deployment Guide

## 📋 Overview
This guide contains all the code changes needed to update the SYARAT button functionality from opening in a new window to showing in a modal popup, with support for `jensetaraf` parameter for E, F, G categories.

## 🎯 Changes Summary
- **STPM programs**: SYARAT button now opens modal popup in same browser
- **E, F, G categories**: Include `jensetaraf` parameter for better filtering
- **Removed**: Print/Cetak functionality from modal
- **Removed**: SPM button support (old code cleanup)

---

## 📁 File Changes Required

### 1. **Frontend View File**
**File**: `UPUCentre/resources/views/mc_profil_calon/index_mpb.blade.php`

#### 1.1 Update Button Click Handler (Line 277)
```php
<!-- BEFORE -->
onclick="showSyaratTest('{{ $fasa_mpb->{'PIL'.$p} }}', '{{ $fasa_mpb->KATAG }}')"

<!-- AFTER -->
onclick="showSyaratTest('{{ $fasa_mpb->{'PIL'.$p} }}', '{{ $fasa_mpb->KATAG }}'@if(in_array($fasa_mpb->KATAG, ['E', 'F', 'G'])), '{{ $fasa_mpb->JENSETARAF }}'@endif)"
```

#### 1.2 Remove SPM Button Support (Lines 275-287)
```php
<!-- REPLACE THIS ENTIRE BLOCK -->
@if(request()->input('JENPROG')=='stpm')
<button type="button" class="btn btn-info btn-sm mt-1"
        onclick="showSyaratTest('{{ $fasa_mpb->{'PIL'.$p} }}', '{{ $fasa_mpb->KATAG }}')"
        title="Lihat Kelayakan Minimum Program">
    <i class="fas fa-list-alt"></i> Syarat
</button>
@else
<button type="button" class="btn btn-info btn-sm mt-1"
        onclick="showSyaratModal('{{ $fasa_mpb->KATAG }}', '{{ $fasa_mpb->{'PIL'.$p} }}')"
        title="Lihat Syarat Program">
    <i class="fas fa-list-alt"></i> Syarat
</button>
@endif

<!-- WITH THIS -->
@if(request()->input('JENPROG')=='stpm')
<button type="button" class="btn btn-info btn-sm mt-1"
        onclick="showSyaratTest('{{ $fasa_mpb->{'PIL'.$p} }}', '{{ $fasa_mpb->KATAG }}'@if(in_array($fasa_mpb->KATAG, ['E', 'F', 'G'])), '{{ $fasa_mpb->JENSETARAF }}'@endif)"
        title="Lihat Kelayakan Minimum Program">
    <i class="fas fa-list-alt"></i> Syarat
</button>
@endif
```

#### 1.3 Add Modal HTML Structure (After line 746)
```html
{{-- Syarat Program Modal --}}
<div class="modal fade" id="syaratModal" tabindex="-1" role="dialog" aria-labelledby="syaratModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="syaratModalLabel">
                    <i class="fas fa-list-alt"></i> Syarat Program
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="syaratContent" style="max-height: 600px; overflow-y: auto;">
                <div class="text-center">
                    <i class="fas fa-spinner fa-spin text-info"></i>
                    <p class="mt-2">Memuat syarat program...</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fas fa-times"></i> Tutup
                </button>
            </div>
        </div>
    </div>
</div>
```

#### 1.4 Replace JavaScript Function (Around line 760-770)
```javascript
// REPLACE THE OLD showSyaratTest FUNCTION WITH THIS:
// Show Syarat Test - opens specific program requirements in modal popup
function showSyaratTest(kodprogram, kategori, jensetaraf = '') {
    // Show modal
    $('#syaratModal').modal('show');
    
    // Update modal title
    $('#syaratModalLabel').html('<i class="fas fa-list-alt"></i> Syarat Program: ' + kodprogram + ' (Kategori ' + kategori + ')');
    
    // Show loading content
    $('#syaratContent').html(`
        <div class="text-center">
            <i class="fas fa-spinner fa-spin text-info"></i>
            <p class="mt-2">Memuat syarat program untuk <strong>` + kodprogram + `</strong>...</p>
            <small class="text-muted">Kategori: ` + kategori + (jensetaraf ? `, Jensetaraf: ` + jensetaraf : '') + `</small>
        </div>
    `);
    
    // Construct URL for popup program view
    var url = '{{ url("stpm/esyaratkhas/popup") }}/' + kodprogram;
    if (kategori) {
        url += '/' + kategori;
    }
    // Add jensetaraf as query parameter for E, F, G categories
    if (jensetaraf && ['E', 'F', 'G'].includes(kategori)) {
        url += '?jensetaraf=' + jensetaraf;
    }
    
    // Load content via AJAX instead of opening new window
    $.ajax({
        url: url,
        method: 'GET',
        success: function(response) {
            $('#syaratContent').html(response);
        },
        error: function(xhr) {
            $('#syaratContent').html(`
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Maaf!</strong> Ralat memuat syarat program
                    <br><small>Kod Program: ` + kodprogram + `, Kategori: ` + kategori + `</small>
                </div>
            `);
        }
    });
}
```

---

### 2. **Backend Controller File**
**File**: `UPUCentre/app/Http/Controllers/es_syarat/stpm/es_SyaratKhasCTRL.php`

#### 2.1 Update Controller Method Signature (Line 179)
```php
// BEFORE
public function showSpecificProgramPopup($programCode, $kategori = null)

// AFTER  
public function showSpecificProgramPopup(Request $request, $programCode, $kategori = null)
```

#### 2.2 Add JENSETARAF Query Parameter Handling (After line 189)
```php
// ADD THIS AFTER: $sessionSesi = session()->get('sesi_semasa');

// Get jensetaraf from query parameter
$jensetaraf = $request->query('jensetaraf');
```

#### 2.3 Add JENSETARAF Filter to Query (After line 200)
```php
// ADD THIS AFTER: if ($kategori) { $query->where('KATEGORI', $kategori); }

// Add jensetaraf filter for E, F, G categories
if ($jensetaraf && in_array($kategori, ['E', 'F', 'G'])) {
    $query->where('JENSETARAF', $jensetaraf);
}
```

#### 2.4 Update View Compact Statement (Line 218-219)
```php
// BEFORE
return view('es_syaratkhas.stpm.popup', compact('program','codeset_oku','sksubjek',
    'codeset_muet','codeset_muet2','codeset_tstam','programCode','kategori'));

// AFTER
return view('es_syaratkhas.stpm.popup', compact('program','codeset_oku','sksubjek',
    'codeset_muet','codeset_muet2','codeset_tstam','programCode','kategori','jensetaraf'));
```

---

## 🚀 Deployment Steps

### Step 1: Backup Current Files
```bash
# Backup the files before making changes
cp resources/views/mc_profil_calon/index_mpb.blade.php resources/views/mc_profil_calon/index_mpb.blade.php.backup
cp app/Http/Controllers/es_syarat/stpm/es_SyaratKhasCTRL.php app/Http/Controllers/es_syarat/stpm/es_SyaratKhasCTRL.php.backup
```

### Step 2: Apply Changes
1. Update `index_mpb.blade.php` with all the frontend changes above
2. Update `es_SyaratKhasCTRL.php` with all the backend changes above

### Step 3: Clear Caches
```bash
php artisan view:clear
php artisan route:clear
php artisan config:clear
```

### Step 4: Test the Changes
1. Navigate to the profile page (`mc_profil_calon`)
2. Find an STPM program entry
3. Click the "Syarat" button
4. Verify it opens in modal popup (not new window)
5. Test with E, F, G categories to ensure jensetaraf filtering works

---

## 🎯 Expected Results

### ✅ **Before Changes:**
- STPM: Opens `popup.blade.php` in **new window**
- SPM: Opens modal popup in same window
- E, F, G categories: No jensetaraf filtering

### ✅ **After Changes:**
- STPM: Opens modal popup in **same browser window**
- SPM: Button removed (old code cleanup)
- E, F, G categories: Include jensetaraf parameter for proper filtering
- No print/cetak functionality in modal

### ✅ **URL Examples:**
- Regular categories: `/stpm/esyaratkhas/popup/UL6482001/A`
- E, F, G categories: `/stpm/esyaratkhas/popup/UL6482001/E?jensetaraf=E1`

---

## 🔍 Troubleshooting

### Issue: Modal doesn't show
- Check browser console for JavaScript errors
- Ensure jQuery and Bootstrap are loaded
- Verify modal HTML is present in DOM

### Issue: AJAX error for E, F, G categories
- Check if jensetaraf parameter is being passed correctly
- Verify controller method accepts Request parameter
- Check database for JENSETARAF column in program_stpm table

### Issue: Content not loading
- Check network tab for AJAX request status
- Verify route `/stpm/esyaratkhas/popup/{programCode}/{kategori}` exists
- Check controller method permissions and database connections

---

## 📝 Notes
- All changes maintain backward compatibility
- No database changes required
- No new routes needed (uses existing popup route)
- Print functionality completely removed as requested
- SPM support removed (old code cleanup)

**Total Files Modified: 2**
**Estimated Deployment Time: 10-15 minutes**
