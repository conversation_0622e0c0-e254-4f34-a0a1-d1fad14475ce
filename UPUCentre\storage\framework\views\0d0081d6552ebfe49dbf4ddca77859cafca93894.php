
<div class="container-fluid">
    <div class="row">
        <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 mb-3">


            <!-- Alert Messages -->
            <?php if(session('success')): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle mr-2"></i>
                    <strong>BERJAYA!</strong> <?php echo e(session('success')); ?>

                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
            <?php endif; ?>

            <?php if(session('error')): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle mr-2"></i>
                    <strong>RALAT!</strong> <?php echo e(session('error')); ?>

                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
            <?php endif; ?>

            <!-- Main Content Cards -->
            <div class="row">
                <!-- Single NOKP Check -->
                <div class="col-lg-6 col-md-12 mb-4">
                    <div class="card h-100">
                        <div class="card-header bg-primary text-white">
                            <div class="d-flex align-items-center">
                                <div class="mr-3">
                                    <i class="fas fa-search fa-2x"></i>
                                </div>
                                <div>
                                    <h5 class="mb-0 font-weight-bold">Semakan Tunggal</h5>
                                    <small class="opacity-75">Semak satu NOKP sahaja</small>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <form action="<?php echo e(route('upuadmin.semakan-single')); ?>" method="POST">
                                <?php echo csrf_field(); ?>
                                <div class="form-group">
                                    <label for="nokp" class="lbl_font lbl_text">NOKP:</label>
                                    <input type="text" class="form-control <?php $__errorArgs = ['nokp'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                           id="nokp" name="nokp" placeholder="Masukkan 12 digit NOKP"
                                           value="<?php echo e(old('nokp')); ?>" required maxlength="12">
                                    <?php $__errorArgs = ['nokp'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    <small class="form-text text-muted">
                                        <i class="fas fa-info-circle"></i> Contoh: 990101011234
                                    </small>
                                </div>
                                <button type="submit" class="btn btn-primary btn-block">
                                    <i class="fas fa-search mr-2"></i> Semak Sekarang
                                </button>
                            </form>

                            <?php if(session('single_result')): ?>
                                <hr>
                                <div class="alert alert-info mt-3 mb-0">
                                    <div class="d-flex align-items-start justify-content-between">
                                        <div class="pr-3">
                                            <h6 class="mb-2"><i class="fas fa-user"></i> Keputusan Semakan Tunggal</h6>
                                            <div class="row">
                                                <div class="col-sm-6">
                                                    <p class="mb-1 lbl_font"><strong>NOKP:</strong>
                                                        <span class="badge badge-primary"><?php echo e(session('single_result.nokp')); ?></span>
                                                    </p>
                                                </div>
                                                <div class="col-sm-6">
                                                    <p class="mb-1 lbl_font"><strong>Status:</strong>
                                                        <?php if(session('single_result.status') === 'Dijumpai'): ?>
                                                            <span class="badge badge-success">
                                                                <i class="fas fa-check"></i> <?php echo e(session('single_result.status')); ?>

                                                            </span>
                                                        <?php else: ?>
                                                            <span class="badge badge-danger">
                                                                <i class="fas fa-times"></i> <?php echo e(session('single_result.status')); ?>

                                                            </span>
                                                        <?php endif; ?>
                                                    </p>
                                                </div>
                                            </div>
                                            <?php if(session('single_result.table')): ?>
                                                <p class="mb-0 lbl_font"><strong>Jenprog:</strong>
                                                    <span class="badge badge-info"><?php echo e(session('single_result.table')); ?></span>
                                                </p>
                                            <?php endif; ?>
                                            <small class="text-muted d-block mt-2"><i class="fas fa-clock"></i> <?php echo e(session('checked_at', now()->format('d/m/Y H:i:s'))); ?></small>
                                        </div>
                                        <div>
                                            <button type="button" class="btn btn-secondary btn-sm" onclick="clearSingleResult()">
                                                <i class="fas fa-times"></i> Tutup
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Excel Upload -->
                <div class="col-lg-6 col-md-12 mb-4">
                    <div class="card h-100">
                        <div class="card-header bg-success text-white">
                            <div class="d-flex align-items-center">
                                <div class="mr-3">
                                    <i class="fas fa-file-excel fa-2x"></i>
                                </div>
                                <div>
                                    <h5 class="mb-0 font-weight-bold">Semakan Batch (Excel)</h5>
                                    <small class="opacity-75">Semak banyak NOKP sekaligus</small>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <form action="<?php echo e(route('upuadmin.semakan-upload')); ?>" method="POST" enctype="multipart/form-data">
                                <?php echo csrf_field(); ?>
                                <div class="form-group">
                                    <label for="excel_file" class="lbl_font lbl_text">Pilih Fail Excel:</label>
                                    <input type="file" class="form-control-file" id="excel_file" name="excel_file" accept=".xlsx,.xls" required>
                                    <small class="form-text text-muted">
                                        <i class="fas fa-info-circle"></i> Format: Excel (.xlsx, .xls)<br>
                                        <i class="fas fa-table"></i> NOKP dalam kolum A, baris 2 ke bawah
                                    </small>
                                </div>
                                <button type="submit" class="btn btn-success btn-block">
                                    <i class="fas fa-upload mr-2"></i> Muat Naik & Semak
                                </button>
                            </form>

                            <?php
                                // Load batch summary and results if token exists (for page reloads)
                                $batchToken = session('excel_batch_token');
                                $batchSummary = null;
                                $batchResults = [];
                                if ($batchToken && Storage::disk('local')->exists("semakan/{$batchToken}.json")) {
                                    $payload = json_decode(Storage::disk('local')->get("semakan/{$batchToken}.json"), true) ?: [];
                                    $batchResults = $payload['results'] ?? [];
                                    $batchSummary = [
                                        'summary' => $payload['summary'] ?? null,
                                        'checked_at' => $payload['checked_at'] ?? null,
                                        'count' => is_array($batchResults) ? count($batchResults) : 0,
                                    ];
                                }
                            ?>

                            <?php if(isset($batchSummary) && $batchSummary && $batchSummary['summary']): ?>
                                <hr>
                                <div class="alert alert-info mt-3 mb-3">
                                    <div class="d-flex align-items-start justify-content-between">
                                        <div class="pr-3">
                                            <h6 class="mb-2"><i class="fas fa-users"></i> Keputusan Semakan Batch</h6>
                                            <div class="d-flex flex-wrap">
                                                <span class="badge badge-primary mr-2 mb-1">Jumlah: <?php echo e($batchSummary['summary']['total']); ?></span>
                                                <span class="badge badge-success mr-2 mb-1">Dijumpai: <?php echo e($batchSummary['summary']['available']); ?></span>
                                                <span class="badge badge-danger mr-2 mb-1">Tidak Dijumpai: <?php echo e($batchSummary['summary']['not_found']); ?></span>
                                            </div>
                                            <?php if($batchSummary['checked_at']): ?>
                                                <small class="text-muted d-block mt-2"><i class="fas fa-clock"></i> Diproses pada: <?php echo e($batchSummary['checked_at']); ?></small>
                                            <?php endif; ?>
                                        </div>
                                        <div>
                                            <form action="<?php echo e(route('upuadmin.semakan-clear-excel')); ?>" method="POST" class="d-inline-block">
                                                <?php echo csrf_field(); ?>
                                                <button class="btn btn-warning btn-sm mr-2"><i class="fas fa-trash mr-1"></i> Bersihkan</button>
                                            </form>
                                            <a href="<?php echo e(route('upuadmin.semakan-export-excel', ['type' => 'excel'])); ?>" class="btn btn-success btn-sm mr-2" target="_blank" rel="noopener">
                                                <i class="fas fa-file-excel mr-1"></i> Excel
                                            </a>
                                            <a href="<?php echo e(route('upuadmin.semakan-export-pdf', ['type' => 'excel'])); ?>" class="btn btn-danger btn-sm" target="_blank" rel="noopener">
                                                <i class="fas fa-file-pdf mr-1"></i> PDF
                                            </a>
                                        </div>
                                    </div>
                                </div>

                                <?php
                                    $results = $batchResults ?? [];
                                    $perPage = 50;
                                    $total = count($results);
                                    $pages = (int) ceil($total / $perPage);
                                    $currentPage = (int) request('p', 1);
                                    if ($currentPage < 1) $currentPage = 1;
                                    if ($currentPage > $pages) $currentPage = $pages;
                                    $start = ($currentPage - 1) * $perPage;
                                    $paged = array_slice($results, max($start,0), $perPage, true);
                                ?>

                                <?php if($total > 0): ?>
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <button type="button" class="btn btn-outline-secondary btn-sm" data-toggle="collapse" data-target="#batchTableWrap" aria-expanded="false" aria-controls="batchTableWrap">
                                            <i class="fas fa-list"></i> Tunjukkan/Sembunyikan Jadual
                                        </button>
                                        <small>Jumlah rekod: <?php echo e($total); ?></small>
                                    </div>

                                    <div class="collapse" id="batchTableWrap">
                                        <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
                                            <table class="table table-striped table-hover mb-0">
                                                <thead class="thead-dark sticky-top">
                                                    <tr>
                                                        <th width="8%">No.</th>
                                                        <th width="35%">NOKP</th>
                                                        <th width="25%">Status</th>
                                                        <th width="32%">Jenprog</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php $__currentLoopData = $paged; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $result): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <tr>
                                                            <td class="text-center font-weight-bold"><?php echo e($start + $loop->iteration); ?></td>
                                                            <td>
                                                                <span class="badge badge-light text-dark"><?php echo e($result['nokp']); ?></span>
                                                            </td>
                                                            <td class="text-center">
                                                                <?php if($result['status'] === 'Dijumpai'): ?>
                                                                    <span class="badge badge-success">
                                                                        <i class="fas fa-check-circle mr-1"></i> <?php echo e($result['status']); ?>

                                                                    </span>
                                                                <?php else: ?>
                                                                    <span class="badge badge-danger">
                                                                        <i class="fas fa-times-circle mr-1"></i> <?php echo e($result['status']); ?>

                                                                    </span>
                                                                <?php endif; ?>
                                                            </td>
                                                            <td class="text-center">
                                                                <?php if($result['table']): ?>
                                                                    <span class="badge badge-info"><?php echo e($result['table']); ?></span>
                                                                <?php else: ?>
                                                                    <span class="text-muted">-</span>
                                                                <?php endif; ?>
                                                            </td>
                                                        </tr>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </tbody>
                                            </table>
                                        </div>

                                        <?php if($pages > 1): ?>
                                            <nav aria-label="Batch pagination" class="mt-2">
                                                <ul class="pagination pagination-sm justify-content-end mb-0">
                                                    <li class="page-item <?php if($currentPage <= 1): ?> disabled <?php endif; ?>">
                                                        <a class="page-link" href="?p=<?php echo e(max($currentPage - 1, 1)); ?>">&laquo;</a>
                                                    </li>
                                                    <li class="page-item disabled">
                                                        <span class="page-link"><?php echo e($currentPage); ?> / <?php echo e($pages); ?></span>
                                                    </li>
                                                    <li class="page-item <?php if($currentPage >= $pages): ?> disabled <?php endif; ?>">
                                                        <a class="page-link" href="?p=<?php echo e(min($currentPage + 1, $pages)); ?>">&raquo;</a>
                                                    </li>
                                                </ul>
                                            </nav>
                                        <?php endif; ?>
                                    </div>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

            </div>

        </div>
    </div>
</div>

<script>
    $(document).ready(function(){
        // File input validation
        $('#excel_file').on('change', function() {
            const file = this.files[0];
            if (file) {
                const fileSize = file.size / 1024 / 1024; // MB
                const fileName = file.name;
                const fileExt = fileName.split('.').pop().toLowerCase();

                if (!['xlsx', 'xls'].includes(fileExt)) {
                    alert('Sila pilih fail Excel (.xlsx atau .xls)');
                    $(this).val('');
                    return;
                }

                if (fileSize > 5) {
                    alert('Saiz fail tidak boleh melebihi 5MB');
                    $(this).val('');
                    return;
                }
            }
        });

        // NOKP input formatting
        $('#nokp').on('input', function() {
            let value = $(this).val().replace(/\D/g, ''); // Remove non-digits
            if (value.length > 12) {
                value = value.substring(0, 12);
            }
            $(this).val(value);
        });
    });

    // Function to clear single result
    function clearSingleResult() {
        if (confirm('Adakah anda pasti untuk menghapuskan keputusan ini?')) {
            $.post('<?php echo e(route("upuadmin.semakan-clear-single")); ?>', {
                _token: '<?php echo e(csrf_token()); ?>'
            }, function(response) {
                location.reload();
            }).fail(function(xhr, status, error) {
                console.log('Clear single failed:', xhr.status, xhr.responseText);
                if (xhr.status === 401 || xhr.status === 419) {
                    alert('Sesi telah tamat. Sila log masuk semula.');
                    window.location.href = '<?php echo e(route("login")); ?>';
                } else {
                    // Fallback: hide the result div
                    $('.alert-info').fadeOut('slow');
                }
            });
        }
    }
</script>
<?php /**PATH C:\xampp\htdocs\UPUCentre\resources\views/mc_rekod_mohon/semakan_pelajar_content.blade.php ENDPATH**/ ?>