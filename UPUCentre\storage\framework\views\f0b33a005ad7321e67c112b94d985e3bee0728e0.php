
<li style="padding-left: .3em;"> 
    <?php $__currentLoopData = $syaratkhas_f1; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $syarat_khas_f1): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        Mendapat sekurang-kurangnya Gred <b><?php echo e($syarat_khas_f1->MINGRED); ?></b> dalam mana-mana <b><?php echo e($syarat_khas_f1->KET_JUMLAH_MIN_SUBJEK); ?> (<?php echo e($syarat_khas_f1->JUMLAH_MIN_SUBJEK); ?>)</b> 
		
        <?php if($syarat_khas_f1->SUB_KUMPULAN=='F'): ?> mata pelajaran.
        <?php elseif($syarat_khas_f1->SUB_KUMPULAN=='X'): ?> mata pelajaran yang belum diambil kira.
        <?php elseif($syarat_khas_f1->SUB_KUMPULAN=='Y'): ?>  mata pelajaran selain diatas.
        <?php endif; ?>
		
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
</li>
<?php /**PATH C:\xampp\htdocs\UPUCentre\resources\views/es_syaratkhas/spm/papar_syarat/syarat_khas_f1.blade.php ENDPATH**/ ?>