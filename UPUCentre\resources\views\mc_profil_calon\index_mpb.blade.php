@if(!empty($mpb))
<div class="col-xl-8 col-lg-8 mb-3" id="watermark_mohon">
    <div class="alert alert-secondary bg-light" role="alert">

        @foreach ($mpb as $fasa_mpb)
            <h4>{{trim($fasa_mpb->NAMA)}}</h4>
            <h5>{{trim($fasa_mpb->NOKP)}} <sup>(


                {{-- {{trim($fasa_mpb->KATAG)}}@if($fasa_mpb->KATAG=='G' || $fasa_mpb->KATAG=='E' || $fasa_mpb->KATAG=='F')/{{trim($fasa_mpb->JENSETARAF)}}@endif --}}
                 {{trim($fasa_mpb->KATAG)}}
                {{-- For E, F, G categories: show JENSETARAF description only --}}
                @if(in_array($fasa_mpb-><PERSON><PERSON><PERSON>, ['E', 'F', 'G']))
                    /{{trim($fasa_mpb->JENSETARAF)}}
                    {{-- Try database first, then fallback --}}
                    @php $found = false; @endphp
                    @foreach($jensetaraf as $jen_setaraf)
                        @if($fasa_mpb->JENSETARAF == $jen_setaraf->kodjensetaraf)
                            - {{ $jen_setaraf->ketjensetaraf }}
                            @php $found = true; @endphp
                            @break
                        @endif
                    @endforeach

                    {{-- Fallback mapping if not found in database --}}
                    @if(!$found)
                        @switch($fasa_mpb->JENSETARAF)
                            @case('E1') - DKM / DLKM @break
                            @case('E2') - DVM @break
                            @case('E3') - DIPLOMA ILKA / IPTS / LUAR NEGARA / LAIN-LAIN DIPLOMA @break
                            @case('E4') - DIPLOMA ILKA @break
                            @case('F1') - A-LEVEL @break
                            @case('F2') - IB @break
                            @case('F3') - SEKOLAH SUKAN (SSB) / SSTMI / SSMP @break
                            @case('F4') - STPM BUKAN TAHUN SEMASA @break
                            @case('F5') - MATRIKULASI KPM / ASASI (BUKAN TAHUN SEMASA)@break
                            @case('F6') - STAM BUKAN TAHUN SEMASA @break
                            @case('F7') - AUSMAT / SAM / ADFP @break
                            @case('F8') - ASASI IPTS @break
                            @case('F9') - UEC (SENIOR MIDDLE THREE)@break
                            @case('G1') - DIPLOMA UA @break
                            @case('G2') - DIPLOMA POLITEKNIK @break
                        @endswitch
                    @endif
                @else
                    {{-- For other categories: show KATAG description only --}}
                    @switch($fasa_mpb->KATAG)
                        @case('A')
                            @if(request()->input('JENPROG')=='spm')
                                - KATEGORI A
                            @else
                                - STPM SASTERA
                            @endif
                            @break
                        @case('B')
                            @if(request()->input('JENPROG')=='spm')
                                - KATEGORI B
                            @else
                                - KATEGORI B
                            @endif
                            @break
                        @case('S')
                            @if(request()->input('JENPROG')=='stpm')
                                - STPM SAINS
                            @else
                                - STPM (SAINS)
                            @endif
                            @break
                        @case('P') - MATRIKULASI (AKAUN)@break
                        @case('L') - ASASI (TESL)@break
                        @case('U') - ASASI (UNDANG-UNDANG)@break
                        @case('N') - MATRIKULASI (SAINS)@break
                        @case('K') - ASASI (KEJURUTERAAN)@break
                        @case('J') - MATRIKULASI (TEKNIKAL)@break
                        @case('M') - ASASI (SOSIAL)@break
                        @case('V') - ASASI (TVET)@break
                        @case('T') - STAM @break
                    @endswitch
                @endif



                )<span style="font-family: consolas;font-size:small" class="text-muted">[{{ (int)trim($fasa_mpb->MERIT) }}]</span></sup></h5>
            <span class="text-primary">(SESI 20{{ substr(request()->input('SESIAKAD'),0,2)}} / 20{{ substr(request()->input('SESIAKAD'),2,2) }})</span class="text-primary">
            <hr>

            @if(request()->input('SESIAKAD') > '2223')
            <button type="button" class="btn btn-primary btn-sm mb-3" data-nokp="{{ request()->input('NOKP')}}" data-toggle="modal" data-target="#mpb_{{ request()->input('NOKP')}}" data-keyboard="false" data-backdrop="static">
                <i class="fas fa-fw fa-info-circle"></i> Maklumat Terperinci
            </button>
            @endif

            {{-- Syarat Test Button --}}
            {{-- @if(request()->input('JENPROG')=='stpm' && !empty($fasa_mpb->KURJAYA))
                @php
                    // Extract program code from KURJAYA
                    $programCode = $fasa_mpb->KURJAYA;
                    $kategori = $fasa_mpb->KATAG;
                @endphp
                <button type="button" class="btn btn-success btn-sm mb-3 ml-2" onclick="showSyaratTest('{{ $programCode }}', '{{ $kategori }}')" title="Lihat Kelayakan Minimum untuk program ini">
                    <i class="fas fa-fw fa-clipboard-check"></i> Syarat Test
                </button>
            @endif --}}

            @if(Auth::user()->roles=='1' || Auth::user()->roles=='2')
                @foreach($nama_pengguna as $upu_user)
                    @if(Auth::user()->user_id == $upu_user->user_id)
                    <div class="form-row">
                        <div class="form-group col-md-12">
                            <h6 class="border-bottom badge-line"><label for="mohon" class="mb-n2 badge badge-info rounded-sm">Jenis Prajaya</label></h6>
                                @foreach($jen_pjaya as $pjaya)
                                    @if($pjaya->kodjenpjaya==$fasa_mpb->JEN_PJAYA)
                                        {{$pjaya->ketjenpjaya}}
                                    @endif
                                @endforeach

                                @if($fasa_mpb->KURJAYA=='' && $fasa_mpb->KURJAYA2=='')
                                    -----------------------
                                @endif

                        </div>
                    </div>
                    @endif
                @endforeach
            @endif

            <div class="form-row">
                <div class="form-group col-md-12">
                    <h6 class="border-bottom badge-line"><label for="mohon" class="mb-n2 badge badge-info rounded-sm">Program Jaya</label></h6>
                        @php
                        if(request()->input('JENPROG')=='spm')
                        {
                            $kursus_sql  = \DB::connection('mysql_online')->select(DB::raw("SELECT * FROM 02_all_kodspm"));
                        }
                        elseif(request()->input('JENPROG')=='stpm')
                        {
                            $kursus_sql  = \DB::connection('mysql_online')->select(DB::raw("SELECT * FROM 01_all_kodstpm"));
                        }
                        @endphp

                        @foreach($kursus_sql as $kurjaya)
                            @if($kurjaya->kod==$fasa_mpb->KURJAYA)
                            <b>{{$kurjaya->kod}} - {{$kurjaya->ipta}} {{$kurjaya->program}} @if($kurjaya->tduga=='Y') # @endif</b>
                            <br><span class="badge mt-1" style="background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7;">
                                @foreach($jen_pjaya as $pjaya)
                                    @if($pjaya->kodjenpjaya==$fasa_mpb->JEN_PJAYA)
                                        {{-- {{$fasa_mpb->JEN_PJAYA}} -  --}}
                                        {{$pjaya->ketjenpjaya}}
                                    @endif
                                @endforeach
                            </span>
                            @endif
                        @endforeach



                        @if($fasa_mpb->KURJAYA=='')
                        @if(trim($fasa_mpb->SBBXLYK) != '')
                        <span class="text-danger font-weight-bold">
                        {{ $fasa_mpb->SBBXLYK }} -
                        @foreach ($sbbxlayak as $sbb_xlayak)
                        @if(trim($fasa_mpb->SBBXLYK) == trim($sbb_xlayak->kodsbbxlyk))
                        {{ $sbb_xlayak->ketsbbxlyk }}
                        @endif
                        @endforeach
                        </span>
                        @else
                        Tiada Tawaran
                        @endif
                        @endif
                </div>
            </div>

            <div class="form-row">
                <div class="form-group col-md-12">
                    <h6 class="border-bottom badge-line"><label for="mohon" class="mb-n2 badge badge-info rounded-sm">Lain-lain Kurjaya</label></h6>
                        @if(request()->input('JENPROG')=='spm')
                         @if(TRIM($fasa_mpb->KURJAYA2!='')){{ $fasa_mpb->KURJAYA2 }} @else TIADA @endif
                        @else
                        @if(TRIM($fasa_mpb->KURJAYA2!='')){{ $fasa_mpb->KURJAYA2 }} @else TIADA @endif
                        @endif
                </div>
            </div>

            <div class="form-row">
                <div class="form-group col-md-12">
                    <h6 class="border-bottom badge-line"><label for="mohon" class="mb-n2 badge badge-info rounded-sm">Tajaan</label></h6>
                        @if(request()->input('JENPROG')=='spm')
                         @if(TRIM($fasa_mpb->KURJAYA3!='')){{ $fasa_mpb->KURJAYA3 }} @else TIADA @endif
                        @else
                        @if(TRIM($fasa_mpb->KURJAYA3!='')){{ $fasa_mpb->KURJAYA3 }} @else TIADA @endif
                        @endif
                </div>
            </div>


        @endforeach

        <div class="table-responsive">
            <table class="table table-sm table-bordered tbl_font" id="tblprofil">
                <thead class="thead-dark">
                    <tr>
                        <th colspan="10">Pilihan Program</th>
                    </tr>
                </thead>
                <thead>
                    <tr>
                        <th style="width:50px; vertical-align:middle; text-align:center; font-weight:bold;">Pilihan</th>
                        <th style="width:80px; font-weight:bold; vertical-align:middle; text-align:center;">Institusi / <br> Agensi</th>
                        <th style="width:100px; font-weight:bold; vertical-align:middle; text-align:center;">Kod</th>
                        <th style="font-weight:bold; vertical-align:middle;">Program<br /><strong></strong></th>

                        {{-- @if(Auth::user()->roles=='1' || Auth::user()->roles=='2')
                        <th style="width:80px; font-weight:bold; vertical-align:middle; text-align:center;">Jumlah <br> Mohon</th>
                        <th style="width:80px; font-weight:bold; vertical-align:middle; text-align:center;">Jumlah <br> Tawar</th>
                        @endif --}}

                        @if(request()->input('SESIAKAD') == '2526' || request()->input('SESIAKAD') == '2526')
                        <th hidden style="width:80px; font-weight:bold; vertical-align:middle; text-align:center;">Purata <br> Markah <br> Merit</th>
                        @endif

                        <th style="width:80px; font-weight:bold; vertical-align:middle; text-align:center;">Lulus Syarat Khas</th>

                        {{-- [origin] @if(request()->input('SESIAKAD') == '2526') --}}
            @if(in_array(request()->input('SESIAKAD'), ['2526']))
                        <th style="width:80px; font-weight:bold; vertical-align:middle; text-align:center;">Panggilan Tduga</th>
                        <th style="width:80px; font-weight:bold; vertical-align:middle; text-align:center;">Peraku <br> Tduga</th>
                        @endif
                    </tr>
                </thead>


                <tbody>
                @foreach ($mpb as $fasa_mpb)

                @php
                    if(request()->input('JENPROG')=='spm'){$pilLimit=12;}
                    if(request()->input('JENPROG')=='stpm' && ($fasa_mpb->KATAG!='G' && $fasa_mpb->KATAG!='E' && $fasa_mpb->KATAG!='F')){$pilLimit=12;}
                    if(request()->input('JENPROG')=='stpm' && ($fasa_mpb->KATAG=='G' || $fasa_mpb->KATAG=='E' || $fasa_mpb->KATAG=='F')){$pilLimit=8;}
                @endphp



                    @for($p=1;$p<=$pilLimit;$p++)

                        @if(request()->input('JENPROG')=='spm')
                        <tr @if(substr($fasa_mpb->KURJAYA,0,1)=='U') @if($fasa_mpb->{'PIL'.$p}==$fasa_mpb->KURJAYA && trim($fasa_mpb->KURJAYA) <> '') class="text-success font-weight-bold" style="font-size:0.9rem;" @endif @else @if($fasa_mpb->{'PIL'.$p}==$fasa_mpb->KURJAYA && $fasa_mpb->{'PPIL'.$p}==$fasa_mpb->KURJAYAPUSAT && trim($fasa_mpb->KURJAYA) <> '') class="text-success font-weight-bold" style="font-size:0.9rem;" @endif @endif @if(trim($fasa_mpb->{'LAY'.$p})!='') class="text-danger"@endif>
                        @endif

                        @if(request()->input('JENPROG')=='stpm')
                        <tr @if(substr($fasa_mpb->KURJAYA,0,1)=='U') @if($fasa_mpb->{'PIL'.$p}==$fasa_mpb->KURJAYA && trim($fasa_mpb->KURJAYA) <> '') class="text-success font-weight-bold" style="font-size:0.9rem;" @endif @endif @if(trim($fasa_mpb->{'LAY'.$p})!='') class="text-danger"@endif>
                        @endif

                            <td style="vertical-align:middle; text-align:center;">{{ $p }}</td>
                            <td style="vertical-align:middle; text-align:center;">
                                @php
                                    if(request()->input('JENPROG')=='spm')
                                    {
                                    $kursus_sql  = \DB::connection('mysql_online')->select(DB::raw("SELECT ipta FROM 02_all_kodspm WHERE kod='".$fasa_mpb->{'PIL'.$p}."'"));
                                    }
                                    elseif(request()->input('JENPROG')=='stpm')
                                    {
                                        $kursus_sql  = \DB::connection('mysql_online')->select(DB::raw("SELECT ipta FROM 01_all_kodstpm WHERE kod='".$fasa_mpb->{'PIL'.$p}."'"));
                                    }
                                @endphp

                                @foreach($kursus_sql as $kursus_row)
                                    {{$kursus_row->ipta}}
                                @endforeach
                            </td>
                            <td style="vertical-align:middle; text-align:center;">
                                {{ $fasa_mpb->{'PIL'.$p} }}
                                @if(trim($fasa_mpb->{'PIL'.$p}) != '')
                                    <br>
                                    @if(request()->input('JENPROG')=='stpm')
                                    <button type="button" class="btn btn-info btn-sm mt-1"
                                            onclick="showSyaratTest('{{ $fasa_mpb->{'PIL'.$p} }}', '{{ $fasa_mpb->KATAG }}'@if(in_array($fasa_mpb->KATAG, ['E', 'F', 'G'])), '{{ $fasa_mpb->JENSETARAF }}'@endif)"
                                            title="Lihat Kelayakan Minimum Program">
                                        <i class="fas fa-list-alt"></i> Syarat
                                    </button>
                                    @endif
                                @endif
                            </td>
                            <td>
                                @php
                                    if(request()->input('JENPROG')=='spm')
                                    {
                                        $kursus_sql  = \DB::connection('mysql_online')->select(DB::raw("SELECT program,tduga FROM 02_all_kodspm WHERE kod='".$fasa_mpb->{'PIL'.$p}."'"));
                                        $pstlatihan_sql  = \DB::connection('mysql_online')->select(DB::raw("SELECT pusatlatihan FROM 02_pusat_latihan WHERE kodkursusbaru='".$fasa_mpb->{'PIL'.$p}."' AND kodpusatlatihan='".$fasa_mpb->{'PPIL'.$p}."'"));
                                    }
                                    elseif(request()->input('JENPROG')=='stpm')
                                    {
                                        $kursus_sql  = \DB::connection('mysql_online')->select(DB::raw("SELECT program,tduga FROM 01_all_kodstpm WHERE kod='".$fasa_mpb->{'PIL'.$p}."'"));
                                    }
                                @endphp

                                @foreach($kursus_sql as $kursus_row)
                                    {{$kursus_row->program}} @if($kursus_row->tduga=='Y') # @endif
                                @endforeach


                                {{-- NEW: Add PNGK and MARKOKO Range Display --}}
                                @if(request()->input('JENPROG')=='stpm' && request()->input('SESIAKAD') != '' && TRIM($fasa_mpb->{'PIL'.$p}) != '')
                                    @php
                                        // Get session value from dropdown selection
                                        $sesiakad = request()->input('SESIAKAD');

                                        // Map session to table name
                                        if(request()->input('JENPROG')=='stpm') {
                                            if($sesiakad=='2122') {
                                                $tableName = '2122_stpm_utama';
                                            }
                                            elseif($sesiakad=='2223') {
                                                $tableName = '2223_stpm_utama';
                                            }
                                            elseif($sesiakad=='2324') {
                                                $tableName = '2324_stpm_utama';
                                            }
                                            elseif($sesiakad=='2425') {
                                                $tableName = '2425_stpm_utama';
                                            }
                                            elseif($sesiakad=='2526') {
                                                $tableName = '2526_stpm_utama';
                                            }
                                            else {
                                                $tableName = '2526_stpm_utama'; // fallback
                                            }
                                        }

                                        // Determine JEN_PJAYA filter based on KATAG
                                        if(in_array($fasa_mpb->KATAG, ['E', 'F', 'G'])) {
                                            $jenPjayaFilter = "JEN_PJAYA = '9'";
                                            $labelType = "Diploma Setaraf";
                                        } else {
                                            $jenPjayaFilter = "JEN_PJAYA IN('0','1')";
                                            $labelType = "Prajaya";
                                        }

                                        // Get PNGK range for this program and student category
                                        [$pngkMax, $pngkMin] = \Cache::remember('pngk_range_'.$fasa_mpb->{'PIL'.$p}.'_'.$fasa_mpb->KATAG.'_'.$sesiakad, 22800, function () use ($fasa_mpb, $p, $tableName, $jenPjayaFilter) {
                                            // Get MAX PURATAPNGK with CAST to ensure numeric comparison
                                            $maxResult = \DB::connection('upu_cloud')->select(\DB::raw("SELECT MAX(CAST(PURATAPNGK AS DECIMAL(5,2))) as max_pngk FROM {$tableName} WHERE KURJAYA = ? AND {$jenPjayaFilter} AND PURATAPNGK IS NOT NULL AND PURATAPNGK != ''"), [$fasa_mpb->{'PIL'.$p}]);

                                            // Get MIN PURATAPNGK with CAST to ensure numeric comparison
                                            $minResult = \DB::connection('upu_cloud')->select(\DB::raw("SELECT MIN(CAST(PURATAPNGK AS DECIMAL(5,2))) as min_pngk FROM {$tableName} WHERE KURJAYA = ? AND {$jenPjayaFilter} AND PURATAPNGK IS NOT NULL AND PURATAPNGK != ''"), [$fasa_mpb->{'PIL'.$p}]);

                                            return [
                                                isset($maxResult[0]) ? $maxResult[0]->max_pngk : null, // max PNGK
                                                isset($minResult[0]) ? $minResult[0]->min_pngk : null // min PNGK
                                            ];
                                        });

                                        // // Get MARKOKO range for this program and student category
                                        // [$markokoMax, $markokoMin] = \Cache::remember('markoko_range_v2_'.$fasa_mpb->{'PIL'.$p}.'_'.$fasa_mpb->KATAG.'_'.$sesiakad, 22800, function () use ($fasa_mpb, $p, $tableName, $jenPjayaFilter) {
                                        //     // Get MAX MARKOKO with CAST and TRIM to ensure numeric comparison
                                        //     $maxResult = \DB::connection('upu_cloud')->select(\DB::raw("SELECT MAX(CAST(TRIM(MARKOKO) AS DECIMAL(10,2))) as max_markoko FROM {$tableName} WHERE KURJAYA = ? AND {$jenPjayaFilter} AND MARKOKO IS NOT NULL AND TRIM(MARKOKO) != '' AND TRIM(MARKOKO) != '0'"), [$fasa_mpb->{'PIL'.$p}]);

                                        //     // Get MIN MARKOKO with CAST and TRIM to ensure numeric comparison
                                        //     $minResult = \DB::connection('upu_cloud')->select(\DB::raw("SELECT MIN(CAST(TRIM(MARKOKO) AS DECIMAL(10,2))) as min_markoko FROM {$tableName} WHERE KURJAYA = ? AND {$jenPjayaFilter} AND MARKOKO IS NOT NULL AND TRIM(MARKOKO) != '' AND TRIM(MARKOKO) != '0'"), [$fasa_mpb->{'PIL'.$p}]);

                                        //     return [
                                        //         isset($maxResult[0]) ? $maxResult[0]->max_markoko : null, // max MARKOKO
                                        //         isset($minResult[0]) ? $minResult[0]->min_markoko : null // min MARKOKO
                                        //     ];
                                        // });
                                        //
                                        // Get MARKOKO range for this program and student category (exclude E, F, G)
                                        if(!in_array($fasa_mpb->KATAG, ['E', 'F', 'G'])) {
                                            [$markokoMax, $markokoMin] = \Cache::remember('markoko_range_v2_'.$fasa_mpb->{'PIL'.$p}.'_'.$fasa_mpb->KATAG.'_'.$sesiakad, 22800, function () use ($fasa_mpb, $p, $tableName, $jenPjayaFilter) {
                                                // Get MAX MARKOKO with CAST and TRIM to ensure numeric comparison
                                                $maxResult = \DB::connection('upu_cloud')->select(\DB::raw("SELECT MAX(CAST(TRIM(MARKOKO) AS DECIMAL(10,2))) as max_markoko FROM {$tableName} WHERE KURJAYA = ? AND {$jenPjayaFilter} AND MARKOKO IS NOT NULL AND TRIM(MARKOKO) != '' AND TRIM(MARKOKO) != '0'"), [$fasa_mpb->{'PIL'.$p}]);

                                                // Get MIN MARKOKO with CAST and TRIM to ensure numeric comparison
                                                $minResult = \DB::connection('upu_cloud')->select(\DB::raw("SELECT MIN(CAST(TRIM(MARKOKO) AS DECIMAL(10,2))) as min_markoko FROM {$tableName} WHERE KURJAYA = ? AND {$jenPjayaFilter} AND MARKOKO IS NOT NULL AND TRIM(MARKOKO) != '' AND TRIM(MARKOKO) != '0'"), [$fasa_mpb->{'PIL'.$p}]);

                                                return [
                                                    isset($maxResult[0]) ? $maxResult[0]->max_markoko : null, // max MARKOKO
                                                    isset($minResult[0]) ? $minResult[0]->min_markoko : null // min MARKOKO
                                                ];
                                            });
                                        } else {
                                            // Set null values for E, F, G categories
                                            $markokoMax = null;
                                            $markokoMin = null;
                                        }

                                    @endphp

                                    @if($pngkMax && $pngkMin)
                                        <br><span class="badge mt-1" style="background-color: #e3f2fd; color: #1565c0; border: 1px solid #90caf9; font-size: 0.75rem;">
                                            PNGK {{$labelType}}: {{$pngkMin}} - {{$pngkMax}}
                                        </span>
                                    @else
                                        {{-- Debug: Show when no PNGK data found --}}
                                        <br><small class="text-muted" style="font-size: 0.7rem;">[No PNGK data for {{$labelType}}]</small>
                                    @endif

                                    @if($markokoMax && $markokoMin)
                                        <br><span class="badge mt-1" style="background-color: #e0f2f1; color: #00695c; border: 1px solid #4db6ac; font-size: 0.75rem;">
                                            MARKOKO {{$labelType}}: {{$markokoMin}} - {{$markokoMax}}
                                        </span>
                                    @else
                                        {{-- Debug: Show when no MARKOKO data found --}}
                                        <br><small class="text-muted" style="font-size: 0.7rem;">[Tiada data MARKOKO untuk {{$labelType}}]</small>
                                    @endif
                                @endif


                                @if(request()->input('JENPROG')=='spm')
                                @foreach($pstlatihan_sql as $pstlatihan_row)
                                    <div><b>Pusat Latihan</b> :
                                        {{$pstlatihan_row->pusatlatihan}}
                                    </div>
                                @endforeach
                                @endif<br />


{{-- script asal merit --}}
{{--
                @if(request()->input('JENPROG')=='spm' && request()->input('SESIAKAD')=='2526' && TRIM($fasa_mpb->{'PIL'.$p}) != '')
                    @php
                        [$merittop, $meritbottom] = \Cache::remember('merit_topbottom_jenpjaya01_'.$fasa_mpb->{'PIL'.$p}, 22800, function () use ($fasa_mpb, $p) {
                                $rows = \DB::connection('upu_cloud')->select(\DB::raw("SELECT MERIT FROM 2526_spm_utama_profile WHERE KURJAYA = ? AND JEN_PJAYA IN('0','1') ORDER BY MERIT"), [$fasa_mpb->{'PIL'.$p}]);

                                $count = count($rows);

                                return [
                                    $count > 0 ? $rows[0]->MERIT : null, // merit awal
                                    $count > 1 ? $rows[$count - 1]->MERIT : ($count > 0 ? $rows[0]->MERIT : null) // merit akhir
                                ];
                        });
                    @endphp

                    {!! '<small style="font-family: consolas; font-weight: bold" class="text-primary">['.$merittop.' <--> '.$meritbottom.']</small>' !!}
                @endif
--}}








@if(request()->input('JENPROG')=='stpm' && request()->input('SESIAKAD') != '' && TRIM($fasa_mpb->{'PIL'.$p}) != '')
    @php
        // Get session value from dropdown selection
        $sesiakad = request()->input('SESIAKAD');

        // Map session to table name (same logic as controller)
        if($sesiakad=='2122') {
            $tableName = '2122_spm_utama';
        }
        elseif($sesiakad=='2223') {
            $tableName = '2223_spm_utama';
        }
        elseif($sesiakad=='2324') {
            $tableName = '2324_spm_utama';
        }
        elseif($sesiakad=='2425') {
            $tableName = '2425_spm_utama_profile';
        }
        elseif($sesiakad=='2526') {
            $tableName = '2526_stpm_utama';
        }
        else {
            $tableName = '2526_stpm_utama'; // fallback
        }

        // Existing merit range code for ETemuduga + COBOL
        [$merittop, $meritbottom] = \Cache::remember('merit_topbottom_jenpjaya01stpmperdana_'.$fasa_mpb->{'PIL'.$p}.$fasa_mpb->KODKAT.'_'.$sesiakad, 22800, function () use ($fasa_mpb, $p, $tableName) {
            $rows = \DB::connection('upu_cloud')->select(\DB::raw("SELECT MERIT FROM {$tableName} WHERE KURJAYA = ? AND KODKAT = ? AND (TRIM(JEN_PJAYA) IN ('0','1')) ORDER BY MERIT"), [$fasa_mpb->{'PIL'.$p}, $fasa_mpb->KODKAT]);

            $count = count($rows);

            return [
                $count > 0 ? $rows[0]->MERIT : null, // merit awal
                $count > 1 ? $rows[$count - 1]->MERIT : ($count > 0 ? $rows[0]->MERIT : null) // merit akhir
            ];
        });



        // NEW: Get max merit for student's specific KURJAYA and KODTARAF
        /*$maxMeritForStudentCategory = \Cache::remember('max_merit_category_'.$fasa_mpb->NOKP.'_'.$fasa_mpb->{'PIL'.$p}.'_'.$sesiakad, 22800, function () use ($fasa_mpb, $p, $tableName) {
            $result = \DB::connection('upu_cloud')->select(\DB::raw("
                SELECT MAX(MERIT) as max_merit
                FROM {$tableName}
                WHERE KURJAYA = ?
                AND KODTARAF = ?
                AND JEN_PJAYA IN('0','1')
            "), [$fasa_mpb->{'PIL'.$p}, $fasa_mpb->KODTARAF]);

            return $result[0]->max_merit ?? null;
        });*/
    @endphp

    {{-- PAPARAN UNTUK KURJAYA, KOTKAT DAN PJAYA --}}
    {!! '<small style="font-family: consolas; font-weight: bold" class="text-primary">['.$merittop.' <--> '.$meritbottom.']</small>' !!}

    {{-- NEW: Display max merit for student's category KOD TARAF--}}
    {{-- @if($maxMeritForStudentCategory)
        <br>
        {!! '<small style="font-family: consolas; font-weight: bold" class="text-success">[Max Merit ('.$fasa_mpb->KODTARAF.'): '.$maxMeritForStudentCategory.']</small>' !!}
    @endif --}}
@endif
</td>

                            @if(Auth::user()->roles=='1' || Auth::user()->roles=='2')
                            {{-- TUTUP MOHON DAN TAWAR --}}
                            {{-- <td style="vertical-align:middle; text-align:center;">
                                @php
                                    if(request()->input('JENPROG')=='spm')
                                    {
                                        $bil_mohon  = \DB::connection('upu_cloud')->select(DB::raw("SELECT * FROM upucenter_profilcalon_spm_mohon WHERE PIL='".$p."' AND KOD='".$fasa_mpb->{'PIL'.$p}."' AND PUSAT='".$fasa_mpb->{'PPIL'.$p}."' AND KOD!=''"));
                                    }
                                    elseif(request()->input('JENPROG')=='stpm')
                                    {
                                        $bil_mohon  = \DB::connection('upu_cloud')->select(DB::raw("SELECT * FROM upucenter_profilcalon_stpm_mohon WHERE PIL='".$p."' AND KOD='".$fasa_mpb->{'PIL'.$p}."' AND KOD!=''"));
                                    }
                                @endphp

                                @foreach ($bil_mohon as $jum_mohon)
                                    @if($jum_mohon->KOD!=''){{ $jum_mohon->BIL_MOHON }} @endif
                                @endforeach
                            </td>

                            <td style="vertical-align:middle; text-align:center;">
                                @php
                                    if(request()->input('JENPROG')=='spm')
                                    {
                                        $bil_tawar  = \DB::connection('upu_cloud')->select(DB::raw("SELECT * FROM upucenter_profilcalon_spm_tawar WHERE PIL='".$p."' AND KOD='".$fasa_mpb->{'PIL'.$p}."' AND PUSAT='".$fasa_mpb->{'PPIL'.$p}."' AND KOD!=''"));
                                    }
                                    elseif(request()->input('JENPROG')=='stpm')
                                    {
                                        $bil_tawar  = \DB::connection('upu_cloud')->select(DB::raw("SELECT * FROM upucenter_profilcalon_stpm_tawar WHERE PIL='".$p."' AND KOD='".$fasa_mpb->{'PIL'.$p}."' AND KOD!=''"));
                                    }
                                @endphp

                                @foreach ($bil_tawar as $jum_tawar)
                                    @if($jum_tawar->KOD!=''){{ $jum_tawar->BIL_TAWAR }} @endif
                                @endforeach
                            </td> --}}
                            @endif

                            @if(request()->input('SESIAKAD') == '2425' || request()->input('SESIAKAD') == '2526')
                            <td hidden style="vertical-align:middle; text-align:center; color:blue;">
                                @php
                                    if(request()->input('JENPROG')=='spm')
                                    {
                                        if(request()->input('SESIAKAD') == '2425'){
                                            $purata_merit  = \DB::connection('kpt')->select(DB::raw("SELECT KODTAWARAN,PURATAMERIT FROM 2425_upu_puratameritspm WHERE KODTAWARAN=?"), [$fasa_mpb->{'PIL'.$p}]);
                                        }
                                        elseif(request()->input('SESIAKAD') == '2526'){
                                            $purata_merit  = \DB::connection('kpt')->select(DB::raw("SELECT programtawaranupu AS KODTAWARAN, puratameritbyyear AS PURATAMERIT FROM vw_programpuratamerit_2526 WHERE programtawaranupu=?"), [$fasa_mpb->{'PIL'.$p}]);
                                        }
                                    }
                                    elseif(request()->input('JENPROG')=='stpm')
                                    {
                                        if($fasa_mpb->KATAG=='T')
                                        {
                                            $purata_merit  = \DB::connection('kpt')->select(DB::raw("SELECT programtawaranupu as KODTAWARAN, puratameritbyyear as PURATAMERIT FROM vw_programpuratamerit_2526 WHERE kategori='STAM' AND programtawaranupu='".$fasa_mpb->{'PIL'.$p}."'"));
                                        }
                                        elseif ($fasa_mpb->KATAG=='P' || $fasa_mpb->KATAG=='L' || $fasa_mpb->KATAG=='U' || $fasa_mpb->KATAG=='N' || $fasa_mpb->KATAG=='K' || $fasa_mpb->KATAG=='J' || $fasa_mpb->KATAG=='A' || $fasa_mpb->KATAG=='S' || $fasa_mpb->KATAG=='M' || $fasa_mpb->KATAG=='V')
                                        {
                                            $purata_merit  = \DB::connection('kpt')->select(DB::raw("SELECT programtawaranupu as KODTAWARAN, puratameritbyyear as PURATAMERIT FROM vw_programpuratamerit_2526 WHERE kategori='STPM/MATRIKULASI/ASASI' AND programtawaranupu='".$fasa_mpb->{'PIL'.$p}."'"));
                                        }
                                    }
                                @endphp

                                @if(($fasa_mpb->KATAG!='E' && $fasa_mpb->KATAG!='F' && $fasa_mpb->KATAG!='G'))
                                    {{-- [origin] @foreach($purata_merit as $purata)
                                        @if($purata->KODTAWARAN==$fasa_mpb->{'PIL'.$p})
                                        @if($purata->PURATAMERIT!='0.00')
                                            <span style="color:blue;">{{$purata->PURATAMERIT}} % <span>
                                        @else
                                            <span style="color:green;"><span>Program <br> Baru</span>
                                        @endif
                                        @endif
                                    @endforeach --}}
                                    @if(isset($purata_merit[0]) && $purata_merit[0]->KODTAWARAN == $fasa_mpb->{'PIL'.$p})
                                        @if($purata_merit[0]->PURATAMERIT!='0.00')
                                            <span style="color:blue;">{{$purata_merit[0]->PURATAMERIT}} % <span>
                                        @else
                                            <span style="color:green;"><span>Program <br> Baru</span>
                                        @endif
                                    @else
                                        {{ 'TIADA' }}
                                    @endif
                                @endif

                                @if(($fasa_mpb->KATAG=='E' || $fasa_mpb->KATAG=='F' || $fasa_mpb->KATAG=='G'))
                                    <span>-<span>
                                @endif
                            </td>
                            @endif

                            <td style="vertical-align:middle; text-align:center;">
                             @if(trim($fasa_mpb->{'LAY'.$p})=='' && trim($fasa_mpb->{'PIL'.$p})<>'') <i class="fa fa-fw fa-check text-success"></i>
                                @elseif(trim($fasa_mpb->{'LAY'.$p})=='*' && trim($fasa_mpb->{'PIL'.$p})<>'') *
                                @elseif(trim($fasa_mpb->{'LAY'.$p})=='?' && trim($fasa_mpb->{'PIL'.$p})<>'') ?
                                @elseif(trim($fasa_mpb->{'LAY'.$p})=='$' && trim($fasa_mpb->{'PIL'.$p})<>'') $
                                @elseif(trim($fasa_mpb->{'LAY'.$p})=='%' && trim($fasa_mpb->{'PIL'.$p})<>'') %
                                @else -
                            @endif

                            </td>

                            {{-- @if(request()->input('SESIAKAD') == '2526') --}}
                @if(in_array(request()->input('SESIAKAD'), ['2526']))
                            <td style="vertical-align:middle; text-align:center;">
                                @php
                                    if(request()->input('JENPROG')=='spm')
                                    {
                                        $kursus_sql  = \DB::connection('mysql_online')->select(DB::raw("SELECT tduga FROM 02_all_kodspm WHERE kod='".$fasa_mpb->{'PIL'.$p}."'"));
                                        $tduga_sql  = \DB::connection('mysql_online')->select(DB::raw("SELECT PANGGIL FROM 700_xfiles_spm_tduga WHERE NOKP='".$fasa_mpb->NOKP."' AND KOD='".$fasa_mpb->{'PIL'.$p}."' GROUP BY NOKP,KOD"));


                                    }
                                    elseif(request()->input('JENPROG')=='stpm')
                                    {
                                        $kursus_sql  = \DB::connection('mysql_online')->select(DB::raw("SELECT tduga FROM 01_all_kodstpm WHERE kod='".$fasa_mpb->{'PIL'.$p}."'"));
                                        $tduga_sql  = \DB::connection('mysql_online')->select(DB::raw("SELECT PANGGIL FROM 700_xfiles_stpm_tduga WHERE NOKP='".$fasa_mpb->NOKP."' AND KOD='".$fasa_mpb->{'PIL'.$p}."' GROUP BY NOKP,KOD"));
                                    }
                                @endphp

                                @foreach($kursus_sql as $kursus_row)

                                    @if($kursus_row->tduga=='Y')

                                        @forelse($tduga_sql as $tduga_row)
                                            @if($tduga_row->PANGGIL=='Y') <i class="fa fa-fw fa-check text-success"></i>
                                            @else <i class="fa fa-fw fa-times text-danger"></i>
                                            @endif
                                        @empty <i class="fa fa-fw fa-times text-danger"></i>

                                        @endforelse

                                    @elseif($kursus_row->tduga=='T')
                                        -
                                    @endif

                                @endforeach
                            </td>
                            <td style="vertical-align:middle; text-align:center;">
                @if(request()->input('SESIAKAD') == '2526')
                @php
                                    if(request()->input('JENPROG')=='spm')
                                    {
                                        $kursus_sql  = \DB::connection('mysql_online')->select(DB::raw("SELECT tduga FROM 02_all_kodspm WHERE kod='".$fasa_mpb->{'PIL'.$p}."'"));
                                        // [origin] $peraku_tduga_sql  = \DB::connection('upu_cloud')->select(DB::raw("SELECT * FROM 2425_spm_peraku_tduga WHERE NOKP='".$fasa_mpb->NOKP."' AND PERAKU='".$fasa_mpb->{'PIL'.$p}."'"));
                                        $peraku_tduga_sql  = \DB::select(DB::raw("SELECT peraku AS PERAKU FROM emas.tbl_intviu_ranking WHERE nokp='".$fasa_mpb->NOKP."' AND peraku='".$fasa_mpb->{'PIL'.$p}."'"));
                                    }
                                    elseif(request()->input('JENPROG')=='stpm')
                                    {
                                        $kursus_sql  = \DB::connection('mysql_online')->select(DB::raw("SELECT tduga FROM 01_all_kodstpm WHERE kod='".$fasa_mpb->{'PIL'.$p}."'"));
                                        $peraku_tduga_sql  = \DB::connection('upu_cloud')->select(DB::raw("SELECT * FROM 2425_stpm_peraku_tduga WHERE NOKP='".$fasa_mpb->NOKP."' AND PERAKU='".$fasa_mpb->{'PIL'.$p}."'"));
                                    }
                                @endphp

                                @foreach($kursus_sql as $kursus_row)

                                    @if($kursus_row->tduga=='Y')

                                        @forelse($peraku_tduga_sql as $peraku_tduga_row)
                                            {{-- [origin] @if($peraku_tduga_row->PERAKU<>'') --}}
                        @if(isset($peraku_tduga_row->PERAKU))
                        <i class="fa fa-fw fa-check text-success"></i>
                                            @else
                        <i class="fa fa-fw fa-times text-danger"></i>
                                            @endif
                                        @empty <i class="fa fa-fw fa-times text-danger"></i>

                                        @endforelse

                                    @elseif($kursus_row->tduga=='T')
                                        -
                                    @endif

                                @endforeach
                @endif
                            </td>
                            @endif
                        </tr>
                    @endfor
                @endforeach
                </tbody>
            </table>
        </div>

        <div class="row mt-n2 mb-3">
            <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12">
                <table class="tbl_font">
                    <tr>
                        <td>CARIAN OLEH : {{ strtoupper(Auth::user()->nama) }}</td>
                    </tr>
                    <tr>
                        <td>JAWATAN : {{ strtoupper(Auth::user()->jawatan) }}</td>
                    </tr>
                </table>
            </div>
        </div>

        <div class="row">
            <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12">
                <table class="tbl_font">
                    <tr>
                        <td class="td tbl_align"><strong>-</strong></td>
                        <td>Tidak Berkenaan</td>
                    </tr>
                    <tr>
                        <td class="td tbl_align"><strong>*</strong></td>
                        <td>Pemohon tidak memenuhi syarat minimum</td>
                    </tr>
                    <tr>
                        <td class="td tbl_align"><strong>?</strong></td>
                        <td>Program Pengajian dimohon tiada dalam senarai kursus pengajian</td>
                    </tr>
                    <tr>
                        <td class="td tbl_align"><strong>$</strong></td>
                        <td>Pemohon Sains mohon Program Pengajian Aliran Sastera</td>
                    </tr>
                    <tr>
                        <td class="td tbl_align"><strong>%</strong></td>
                        <td>Pemohon Sastera mohon Program Pengajian Aliran Sains</td>
                    </tr>
                </table>
            </div>

            <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12">
                <table class="tbl_font">
                    <tr>
                        <td class="td tbl_align"><span class="bg-success">&emsp; &emsp;</span></td>
                        <td> Program ditawarkan / Lulus Syarat</td>
                    </tr>
                    <tr>
                        <td class="td tbl_align"><span class="bg-danger">&emsp; &emsp;</span></td>
                        <td> Tidak lulus syarat</td>
                    </tr>
                </table>
            </div>
        </div>

    </div>
</div>

{{-- Syarat Program Modal --}}
<div class="modal fade" id="syaratModal" tabindex="-1" role="dialog" aria-labelledby="syaratModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="syaratModalLabel">
                    <i class="fas fa-list-alt"></i> Syarat Program
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="syaratContent" style="max-height: 600px; overflow-y: auto;">
                <div class="text-center">
                    <i class="fas fa-spinner fa-spin text-info"></i>
                    <p class="mt-2">Memuat syarat program...</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fas fa-times"></i> Tutup
                </button>
            </div>
        </div>
    </div>
</div>



<script>
    var textWatermark = '{{Auth::user()->user_id}}';
    var body = document.getElementById('watermark_mohon');
    var background = "url(\"data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' version='1.1' height='150px' width='150px'>" +
        "<text transform='translate(20, 100) rotate(-30)' fill='rgba(128,128,128, 0.2)' font-size='20' >" + textWatermark + "</text></svg>\")";
    body.style.backgroundImage = background






    // Show Syarat Test - opens specific program requirements in modal popup
    function showSyaratTest(kodprogram, kategori, jensetaraf = '') {
        // Show modal
        $('#syaratModal').modal('show');

        // Update modal title
        $('#syaratModalLabel').html('<i class="fas fa-list-alt"></i> Syarat Program: ' + kodprogram + ' (Kategori ' + kategori + ')');

        // Show loading content
        $('#syaratContent').html(`
            <div class="text-center">
                <i class="fas fa-spinner fa-spin text-info"></i>
                <p class="mt-2">Memuat syarat program untuk <strong>` + kodprogram + `</strong>...</p>
                <small class="text-muted">Kategori: ` + kategori + (jensetaraf ? `, Jensetaraf: ` + jensetaraf : '') + `</small>
            </div>
        `);

        // Construct URL for popup program view
        var url = '{{ url("stpm/esyaratkhas/popup") }}/' + kodprogram;
        if (kategori) {
            url += '/' + kategori;
        }
        // Add jensetaraf as query parameter for E, F, G categories
        if (jensetaraf && ['E', 'F', 'G'].includes(kategori)) {
            url += '?jensetaraf=' + jensetaraf;
        }

        // Load content via AJAX instead of opening new window
        $.ajax({
            url: url,
            method: 'GET',
            success: function(response) {
                $('#syaratContent').html(response);
            },
            error: function(xhr) {
                $('#syaratContent').html(`
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Maaf!</strong> Ralat memuat syarat program
                        <br><small>Kod Program: ` + kodprogram + `, Kategori: ` + kategori + `</small>
                    </div>
                `);
            }
        });
    }
</script>

@endif
